import { expect, Locator } from '@playwright/test';
import { click, isElementVisible, scrollLocatorIntoView } from '@ui-action';
import { logger, raiseError } from '@report-helper';
import { INSTANT_TIMEOUT } from '@global-timeout';

/**
 * Extracts all data rows (excluding headers) from a Playwright table.
 * @param table - Playwright Locator for the table.
 * @returns An array of row objects: { index, data }
 */
async function getTableRows(table: Locator): Promise<{ index: number; row: Locator; data: Record<string, string> }[]> {
  const headers = (await table.getByRole('columnheader').allInnerTexts()).map(h => h.trim());
  const rows = await table.getByRole('row').all();

  const result: { index: number; row: Locator; data: Record<string, string> }[] = [];

  for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
    const row = rows[rowIndex]!;

    // skip header rows
    if (await row.getByRole('columnheader').count()) continue;

    await scrollLocatorIntoView(row);

    const cells = (await row.getByRole('cell').allInnerTexts()).map(c => c.trim());

    const rowData: Record<string, string> = {};
    headers.forEach((header, idx) => {
      rowData[header] = cells[idx] ?? '';
    });

    result.push({ index: rowIndex, row: row, data: rowData });
  }

  return result;
}

/**
 * Finds the row index in a table matching the given condition.
 * @param table The Playwright Locator for the table.
 * @param condition Key-value pairs to identify the row (e.g., { "Line No.": "1" }).
 * @returns An object with the row index and Locator if found, otherwise index -1 and row undefined.
 */
export async function findRowIndex(
  table: Locator,
  condition: Record<string, string>,
): Promise<{
  index: number;
  row?: Locator | undefined | null;
}> {
  const rows = await getTableRows(table);

  const match = rows.find(r => Object.entries(condition).every(([key, val]) => r.data[key] === val));
  return match
    ? {
        index: match.index,
        row: match.row,
      }
    : { index: -1, row: undefined };
}

/**
 * Verifies a row in an ARIA role-based table matches the given condition and values.
 */
export async function verifyRowData(
  table: Locator,
  condition: string | Record<string, string>,
  values: string[] | Record<string, string>,
): Promise<void> {
  const rows = await getTableRows(table);
  if (typeof condition === 'string' && Array.isArray(values)) {
    // Extract the actual column values from all rows
    const actualValues = rows.map(r => r.data[condition]?.trim() ?? '');
    logger.info(`Actual values for column "${condition}": ${JSON.stringify(actualValues)}`);
    logger.info(`Expected values: ${JSON.stringify(values)}`);
    expect(actualValues.sort()).toEqual(values.sort());
    return;
  }

  const match = rows.find(r => Object.entries(condition).every(([key, val]) => r.data[key] === val));
  if (!match) {
    raiseError(`No matching row found for condition: ${JSON.stringify(condition)}`);
  }

  for (const [key, val] of Object.entries(values)) {
    expect(match.data[key]).toBe(val);
  }
}

/**
 * Expands a row in the table matching the given condition.
 * @param table The Playwright Locator for the table.
 * @param condition Key-value pairs to identify the row (e.g., { "Line No.": "1" }).
 * @returns The Locator of the expanded child table.
 */
export async function expandRow(table: Locator, condition: Record<string, string>): Promise<Locator> {
  const match = await findRowIndex(table, condition);
  if (match?.index === -1) {
    raiseError(`No matching row found for condition: ${JSON.stringify(condition)}`);
  }
  // const childLocator = getLocator(`[role="table"] [role="row"]:nth-of-type(${match.index}) + * [role="table"]`);

  const childLocator = match.row!.locator('xpath=./following-sibling::*[1]').getByRole('table');
  const isExpanded = await isElementVisible(childLocator, { timeout: INSTANT_TIMEOUT });
  if (isExpanded) {
    await scrollLocatorIntoView(childLocator);
    return childLocator;
  }
  const expandIcon = match.row!.locator('a i[class*="list"]');
  await scrollLocatorIntoView(expandIcon);
  await click(expandIcon);
  await scrollLocatorIntoView(childLocator);
  return childLocator;
}
