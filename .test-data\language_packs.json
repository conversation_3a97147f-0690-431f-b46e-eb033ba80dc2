[{"_id": "68c26e13cceee36464441530", "en": {"isEnglish": true, "language": "English", "general": {"department": "Department", "category": "Category", "title": "Title", "company": "Company", "project": "Project", "projectName": "Project Name", "invoice": "Invoice", "timestamp": "Timestamp", "internalComment": "Internal Comment", "emailInformation": "Email Information", "name": "Name", "description": "Description", "supplier": "Supplier", "supplierName": "Supplier Name", "article": "Article", "articleNo": "Article No.", "customer": "Customer", "createdDate": "Created Date", "type": "Type", "sku": "SKU", "quantity": "Quantity", "date": "Date", "year": "Year", "rate": "Rate", "distance": "Distance", "amount": "Amount", "additionalRate": "Additional Rate", "price": "Price"}, "purchaseOrSalesOrder": {"orderNo": "Order No.", "salePriceAfterDiscount": "Sale Price after Discount", "vatCode": "Vat Code", "productSku": "Product SKU", "purchaseOrder": "Purchase Orders", "quantityToDeliver": "Quantity to deliver", "currency": "<PERSON><PERSON><PERSON><PERSON>", "orderStatus": "Order Status", "internalNote": "Internal Note", "deliveryDate": "Delivery Date", "salesPrice": "Sales Price", "purchasePrice": "Purchase Price", "lineText": "Line Text", "warehouse": "Warehouse", "supplierName": "Supplier Name", "registrationDate": "Registration Date", "priceNoK": "Price NOK", "batchNo": "Batch No.", "costPrice": "Cost Price", "premiums": "Premiums", "discount": "Discount", "linePriceNok": "Line Price NOK", "linePrice": "Line Price", "deliveryNo": "Delivery No."}, "buttons": {"send": "Send", "sendToApprove": "Send to approve", "cancel": "Cancel", "ok": "OK", "arrowLeft": "arrow-left", "history": "history", "addFromTemplate": "Add From Template", "saveAndClose": "Save & close", "createPo": "Create PO", "addDeal": "Add deal", "splitRegistration": "Split registration", "reSendStatus": "Resend and status", "override": "Override", "hourOverview": "Hour overview", "createDelivery": "Create delivery", "createInvoice": "Create Invoice", "search": "Search", "addNew": "Add new", "newProject": "New Project", "new": "New", "addNewTickets": "Add New Tickets", "newEquipment": "New Equipment", "generate": "Generate", "save": "Save", "add": "Add", "approve": "Approve", "close": "Close", "moveHours": "Move hours", "finish": "Finish", "runInvoicing": "Run Invoicing", "actions": "Actions", "newRegistration": "New registration", "move": "Move"}, "deal": {"selectServiceToReplace": "Select the service that will be replaced", "leadFrom": "Leads from", "responsiblePerson": "Responsible person", "descriptionIsRequired": "The Description field is required.", "signedAgreement": "Signed agreement", "regenerateFile": "Regenerate file", "showActivities": "Show activities", "commentForCustomer": "Comment for customer", "reviewYourDetailAndSubmit": "Review your details and submit", "agreementDate": "Agreement Date", "invoiceStartDate": "Invoice start date", "agreementNo": "Agreement No", "dealStage": "Deal Stage", "emailWithCustomer": "Email with customer"}, "contact": {"contact": "Contact", "title": "Title", "email": "Email", "firstName": "First Name", "lastName": "Last Name", "inactive": "Inactive", "notes": "Notes"}, "dialogTitle": {"addNewLine": "Add New Line", "agreementConfirmation": "Agreement confirmation", "registerHours": "Register hours", "workOrder": "Work order", "createCustomerInvoice": "Create Customer Invoice", "delivery": "Delivery", "editContact": "Edit Contact", "quickCreateContact": "Quick create contact"}, "status": {"readyForApproving": "Ready for approving", "delivered": "Delivered", "success": "Success", "draft": "Draft", "invoiced": "Invoiced", "confirmed": "Confirmed", "active": "Active", "inactive": "Inactive", "approved": "Approved"}, "tabName": {"orderLines": "Order Lines", "invoices": "Invoices", "workOrders": "Work Orders", "delivery": "Delivery", "activities": "Activities", "sales": "Sales", "personal": "Personal"}, "hour": {"transportType": "Transport type", "approvedDate": "Approved date", "estimatedHours": "Estimate hours", "person": "Person", "activity": "Activity", "registerHours": "Registered hours", "billableHours": "Billable hours", "invoiced": "Invoiced", "additionalExpNo": "Additional Exp.", "drivingFee": "Driving fee", "fromDate": "From Date", "hoursWorked": "Hours worked", "travelExpenses": "Travel expenses", "additionalExpenses": "Additional expenses", "additionalAmount": "Additional amount", "miscHardware": "Misc. hardware", "miscHardwareAmount": "Misc. hardware amount"}, "customerWeb": {"contacts": "Contacts", "usersWithNoLicense": "Users with no license", "companyInfo": "Company info", "equipment": "Equipment"}}, "no": {"isEnglish": false, "language": "Norwegian", "general": {"department": "Avdeling", "category": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "company": "Firma", "project": "Prosjekt", "projectName": "Prosjektnavn", "invoice": "Faktura", "timestamp": "Tidspunkt", "internalComment": "Internkommentar", "emailInformation": "E-postinformasjon", "name": "Navn", "description": "Beskrivelse", "supplier": "Leverandør", "supplierName": "Leverandørnavn", "article": "<PERSON><PERSON><PERSON>", "articleNo": "Artikkelnr", "customer": "Kunde", "createdDate": "Oppret<PERSON><PERSON> dato", "type": "Type", "sku": "SKU", "quantity": "<PERSON><PERSON><PERSON>", "date": "Da<PERSON>", "year": "<PERSON><PERSON>", "rate": "Rate", "distance": "Avstand", "amount": "<PERSON><PERSON><PERSON>", "additionalRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>"}, "purchaseOrSalesOrder": {"orderNo": "Ordrenr.", "salePriceAfterDiscount": "<PERSON><PERSON><PERSON><PERSON> etter rabatt", "vatCode": "<PERSON><PERSON> kode", "productSku": "Produkt SKU", "purchaseOrder": "Innkjøpsordrer", "quantityToDeliver": "Mengde <PERSON> levere", "currency": "Valuta", "orderStatus": "<PERSON><PERSON><PERSON><PERSON>", "internalNote": "Internnotat", "deliveryDate": "Leveringsdato", "salesPrice": "<PERSON><PERSON><PERSON><PERSON>", "purchasePrice": "<PERSON><PERSON><PERSON><PERSON><PERSON> pris", "lineText": "Linjetekst", "warehouse": "Lager", "supplierName": "Leverandørnavn", "registrationDate": "Registreringsdato", "priceNoK": "<PERSON>ris <PERSON>", "batchNo": "Partinr.", "costPrice": "<PERSON><PERSON><PERSON><PERSON>", "premiums": "Påslag", "discount": "Ra<PERSON><PERSON>", "linePriceNok": "Linjepris NOK", "linePrice": "<PERSON><PERSON><PERSON><PERSON>", "deliveryNo": "Leveranse"}, "buttons": {"send": "Send", "sendToApprove": "Send til <PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "arrowLeft": "arrow-left", "history": "history", "addFromTemplate": "Legg til fra mal", "saveAndClose": "Lagre og lukk", "createPo": "Opprett PO", "addDeal": "Ny deal", "splitRegistration": "Delt registrering", "reSendStatus": "Resend og status", "override": "Overstyring", "hourOverview": "Timeoversikt", "createDelivery": "<PERSON><PERSON><PERSON><PERSON> levering", "createInvoice": "<PERSON><PERSON><PERSON><PERSON> faktura", "search": "<PERSON><PERSON><PERSON>", "addNew": "Ny", "newProject": "Nytt prosjekt", "new": "Ny", "addNewTickets": "Ny oppgave", "newEquipment": "Nytt utstyr", "generate": "<PERSON><PERSON>", "save": "Lagre", "add": "Legg til", "approve": "<PERSON><PERSON><PERSON><PERSON>", "close": "Lukk", "moveHours": "Flytt timer", "finish": "<PERSON><PERSON><PERSON>", "runInvoicing": "<PERSON><PERSON><PERSON><PERSON>ak<PERSON>", "actions": "<PERSON><PERSON>", "newRegistration": "Ny registrering", "move": "<PERSON><PERSON>"}, "deal": {"selectServiceToReplace": "Velg tjenesten som skal erstattes", "leadFrom": "Lead fra", "responsiblePerson": "Ans<PERSON><PERSON>g person", "descriptionIsRequired": "Beskrivelse-feltet er obligatorisk.", "signedAgreement": "<PERSON><PERSON> avtale", "regenerateFile": "Regenerate file", "showActivities": "Vis aktiviteter", "commentForCustomer": "Kommentar til kunde", "reviewYourDetailAndSubmit": "Review your details and submit", "agreementDate": "Avtaledato", "invoiceStartDate": "Faktura startdato", "agreementNo": "Avtalenr.", "dealStage": "Steg", "emailWithCustomer": "Email with customer"}, "contact": {"contact": "Kontaktperson", "title": "<PERSON><PERSON><PERSON>", "email": "E-post", "firstName": "Fornavn", "lastName": "Etternavn", "inactive": "Inaktiv", "notes": "Notater"}, "dialogTitle": {"addNewLine": "<PERSON>gg til linje", "agreementConfirmation": "Avtale bekreftelse", "registerHours": "Registrerte timer", "workOrder": "Work order", "createCustomerInvoice": "<PERSON><PERSON><PERSON><PERSON> faktura", "delivery": "Leveres", "editContact": "<PERSON>iger kontakt", "quickCreateContact": "Rask opprettelse av kontakt"}, "status": {"readyForApproving": "Klar for god<PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON>", "draft": "Kladd", "invoiced": "Fak<PERSON>rt", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktive", "inactive": "Inaktive", "approved": "<PERSON><PERSON><PERSON><PERSON>"}, "tabName": {"orderLines": "<PERSON><PERSON><PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON><PERSON>", "workOrders": "Arbeidsordre", "delivery": "Leveranse", "activities": "Aktiviteter", "sales": "<PERSON><PERSON>", "personal": "<PERSON><PERSON>"}, "hour": {"transportType": "Kjøretøy", "approvedDate": "<PERSON><PERSON><PERSON><PERSON> dato", "estimatedHours": "Estimat", "person": "Personer", "activity": "Aktivitet", "registerHours": "Registrerte timer", "billableHours": "Fakturerbare timer", "invoiced": "Fak<PERSON>rt", "additionalExpNo": "Tilleggskostnader", "drivingFee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fromDate": "<PERSON>a dato", "hoursWorked": "Timer", "travelExpenses": "Reiseutgifter", "additionalExpenses": "<PERSON><PERSON><PERSON>utgi<PERSON>", "additionalAmount": "<PERSON><PERSON><PERSON> for tilleggsutgifter", "miscHardware": "Div. hardware", "miscHardwareAmount": "Beløp for div. hardware"}, "customerWeb": {"contacts": "<PERSON><PERSON><PERSON>", "usersWithNoLicense": "Brukere uten lisens", "companyInfo": "Firmainformasjon", "equipment": "Utstyr"}}}]