import type { APIResponse } from '@playwright/test';
import { handleException, logger, raiseError } from '@report-helper';
import { ContextManager, getGlobalContextManager } from './context-manager.js';
import { applyAuthentication } from './auth-helper.js';
import type { ApiClientConfig, ApiError, ApiRequestOptions, AuthConfig, AuthContext } from '@custom-types';
import { getTestInfo } from '@page-utils'; // ===============================================

// ===============================================
// API Client
// ===============================================

export class ApiClient {
  private contextManager: ContextManager;
  private config: Required<ApiClientConfig>;

  constructor(config?: Partial<ApiClientConfig>) {
    // Set default configuration
    this.config = {
      baseUrl: config?.baseUrl || process.env['API_BASE_URL'] || process.env['BASE_URL'] || '',
      defaultTimeout: config?.defaultTimeout || 30000,
    };

    // Initialize context manager
    this.contextManager = getGlobalContextManager();

    logger.info(`[API Client] Initialized with base URL: ${this.config.baseUrl}`);
  }

  // ===============================================
  // Context Management
  // ===============================================

  /**
   * Add a new authentication context
   */
  async addContext(config: AuthConfig, baseUrl?: string): Promise<string> {
    try {
      const contextBaseUrl = baseUrl || this.config.baseUrl;
      if (!contextBaseUrl) {
        raiseError('Base URL is required. Provide it in config or client configuration.');
      }

      const contextId = await this.contextManager.createContext(config, contextBaseUrl);
      logger.info(`[API Client] Added context '${contextId}'`);
      return contextId;
    } catch (error) {
      handleException(error, `[API Client] Failed to add context`);
    }
  }

  /**
   * Remove an authentication context
   */
  async removeContext(contextId: string): Promise<void> {
    try {
      await this.contextManager.removeContext(contextId);
      logger.info(`[API Client] Removed context '${contextId}'`);
    } catch (error) {
      handleException(error, `[API Client] Failed to remove context '${contextId}'`);
    }
  }

  /**
   * Get all available contexts
   */
  getContexts(): string[] {
    return this.contextManager.getAllContexts().map(ctx => ctx.id);
  }

  /**
   * Get active contexts
   */
  getActiveContexts(): string[] {
    return this.contextManager.getActiveContexts().map(ctx => ctx.id);
  }

  // ===============================================
  // Request Methods
  // ===============================================

  /**
   * Make a request with automatic context selection or specific context
   */
  async request(options: ApiRequestOptions): Promise<APIResponse> {
    const startTime = Date.now();

    try {
      // Determine which context to use
      const authContext = this.selectContext(options.contextId);
      if (!authContext) {
        raiseError(
          options.contextId ? `Context '${options.contextId}' not found or inactive` : 'No active contexts available',
        );
      }

      // Apply authentication to the request
      const authenticatedOptions = await applyAuthentication(options, authContext.config);

      // Build full URL
      const fullUrl = this.buildFullUrl(authenticatedOptions.url, authContext.baseUrl);

      // Log the request
      this.logRequest(authenticatedOptions, authContext.id, fullUrl);

      // Make the request
      const response = await this.executeRequest(authenticatedOptions, authContext, fullUrl);

      // Log the response
      await this.logResponse(response, authContext.id, startTime);

      return response;
    } catch (error) {
      const apiError = error as ApiError;
      apiError.requestOptions = options;

      handleException(apiError, `[API Client] Request failed`);
    }
  }

  /**
   * GET request
   */
  async get(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'GET', url, ...options });
  }

  /**
   * POST request
   */
  async post(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'POST', url, ...options });
  }

  /**
   * PUT request
   */
  async put(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PUT', url, ...options });
  }

  /**
   * PATCH request
   */
  async patch(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'PATCH', url, ...options });
  }

  /**
   * DELETE request
   */
  async delete(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>): Promise<APIResponse> {
    return this.request({ method: 'DELETE', url, ...options });
  }

  // ===============================================
  // Private Helper Methods
  // ===============================================

  private selectContext(contextId?: string): AuthContext | undefined {
    if (contextId) {
      return this.contextManager.getContext(contextId);
    }
    return this.contextManager.getNextContext();
  }

  private buildFullUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) {
      return url;
    }

    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    return `${cleanBaseUrl}${cleanUrl}`;
  }

  private async executeRequest(
    options: ApiRequestOptions,
    authContext: AuthContext,
    fullUrl: string,
  ): Promise<APIResponse> {
    const timeout = options.timeout ?? this.config.defaultTimeout;

    // Build query string
    let urlWithQuery = fullUrl;
    if (options.queryParams) {
      const queryString = new URLSearchParams();
      Object.entries(options.queryParams).forEach(([key, value]) => {
        queryString.set(key, String(value));
      });
      const separator = fullUrl.includes('?') ? '&' : '?';
      urlWithQuery = `${fullUrl}${separator}${queryString.toString()}`;
    }

    // Prepare request options for Playwright
    const requestOptions: {
      method: string;
      timeout: number;
      headers?: Record<string, string>;
      data?: unknown;
      form?: { [p: string]: string | number | boolean } | FormData;
    } = {
      method: options.method,
      timeout,
    };

    if (options.headers) {
      requestOptions.headers = options.headers;
    }

    if (options.body !== undefined) {
      requestOptions.data = options.body;
    }

    if (options.form !== undefined) {
      requestOptions.form = options.form;
    }

    // Make the request
    return await authContext.context.fetch(urlWithQuery, requestOptions);
  }

  // ===============================================
  // Logging Methods
  // ===============================================

  private logRequest(options: ApiRequestOptions, contextId: string, fullUrl: string): void {
    const timestamp = new Date().toISOString();
    let body = '';

    if (options.form) {
      if (options.form instanceof FormData) {
        const entries: string[] = [];
        options.form.forEach((value, key) => {
          entries.push(`${key}: ${value instanceof File ? `[File: ${value.name}]` : value}`);
        });
        body = `Body: [FormData] { ${JSON.stringify(entries.join(', '))} }`;
      } else {
        // Plain object case
        const entries = Object.entries(options.form).map(([key, value]) => {
          return `${key}: ${value}`;
        });
        body = `Body: [Object] { ${entries.join(', ')} }`;
      }
    } else if (options.body) {
      body = `Body: ${JSON.stringify(options.body, null, 2)}`;
    }

    const logData = [
      '',
      `Context Id: ${contextId}`,
      `Timestamp: ${timestamp}`,
      `Method: ${options.method}`,
      `FullUrl : ${fullUrl}`,
      `Headers: ${JSON.stringify(options.headers || {}, null, 2)}`,
      `Query Params: ${JSON.stringify(options.queryParams || undefined, null, 2)}`,
      body,
    ].join('\n');

    logger.info(`[API Request] ${options.method} ${fullUrl} [Context: ${contextId}]`);
    logger.info(`${logData}`);
  }

  private async logResponse(response: APIResponse, contextId: string, startTime: number): Promise<void> {
    const timestamp = new Date().toISOString();
    const duration = Date.now() - startTime;

    let body: unknown;
    let bodyTruncated = false;
    let prettyBody: string = '';
    try {
      // Get response body based on content type
      const contentType = response.headers()['content-type'] || '';

      if (contentType.includes('application/json')) {
        body = await response.json();
        prettyBody = JSON.stringify(body, null, 2);
      } else if (contentType.includes('text/') || contentType.includes('application/xml')) {
        prettyBody = await response.text();
        body = prettyBody;
      } else {
        // For binary content, just get basic info
        const buffer = await response.body();
        prettyBody = `[Binary content: ${buffer.length} bytes, type: ${contentType}]`;
        body = prettyBody;
      }

      // Handle large response bodies
      if (typeof body === 'string' && body.length > 1000) {
        bodyTruncated = true;
        const truncatedBody = body.substring(0, 500) + '\n...[truncated]';

        // Attach full response using Playwright's test info
        try {
          await getTestInfo().attach(response.url(), {
            body: body,
            contentType: 'text/plain',
          });
        } catch (error) {
          logger.warn(`[API Client] Could not attach response: ${String(error)}`);
        }
        prettyBody = prettyBody.substring(0, 500) + '\n...[truncated]';
        body = truncatedBody;
      } else if (typeof body === 'object' && body !== null) {
        const bodyStr = JSON.stringify(body, null, 2);
        prettyBody = bodyStr.substring(0, 500) + '...[truncated]';
        if (bodyStr.length > 1000) {
          bodyTruncated = true;
          const truncatedBody = bodyStr.substring(0, 500) + '\n...[truncated]';

          // Attach full response
          try {
            await getTestInfo().attach(response.url(), {
              body: bodyStr,
              contentType: 'application/json',
            });
          } catch (error) {
            logger.warn(`[API Client] Could not attach response: ${String(error)}`);
          }

          body = truncatedBody;
        }
      }
    } catch (error) {
      body = `[Error reading response body: ${String(error)}]`;
    }

    const formattedLog = [
      '',
      `Timestamp: ${timestamp}`,
      `URL: ${response.url()}`,
      `Status Code: ${response.status()}`,
      `Status Text: ${response.statusText()}`,
      `Duration:  ${duration}ms`,
      `Success: ${response.ok()}`,
      `Context Id: ${contextId}`,
      `Body: ${prettyBody}`,
    ].join('\n');

    const statusIcon = response.ok() ? '✅' : '❌';
    logger.info(
      `[API Response] ${statusIcon} ${response.status()} ${response.statusText()} (${duration}ms) [Context: ${contextId}]`,
    );

    if (bodyTruncated) {
      logger.info(`[API Response] Response body truncated.`);
    }

    logger.info(`[API Response Details] ${formattedLog}`);
  }

  // ===============================================
  // Cleanup
  // ===============================================

  /**
   * Dispose of all contexts and cleanup resources
   */
  async dispose(): Promise<void> {
    try {
      await this.contextManager.dispose();
      logger.info('[API Client] Disposed successfully');
    } catch (error) {
      handleException(error, '[API Client] Error during disposal');
    }
  }
}
