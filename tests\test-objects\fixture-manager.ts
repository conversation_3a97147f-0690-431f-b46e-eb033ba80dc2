import { expect, test as baseTest } from '@global-config';
import path from 'path';
import { acquireAccount } from '@test-data/handlers/account-manager.handler';
import { handleException } from '@report-helper';
import { autoLogin } from '@components/login.func';
import { <PERSON>rowser } from '@playwright/test';
import PurchaseOrderPage from '@page-objects/purchase-order.page';
import process from 'node:process';
import { API_KEY_VALUE, CONTEXT_COOKIE, CONTEXT_KEY, createApiKeyAuth, getGlobalApiClient } from '@api-helper';
import { getCookie } from '@page-utils';

function getFilePath(id: number): string {
  return path.resolve(process.env['AUTH'] || '.auth', process.env['NODE_ENV'] || '', `${id}.json`);
}

// Reusable helper to acquire / cache auth storage state for a role
async function authForRole(
  role: 'Admin' | 'Other',
  browser: Browser,
  use: (statePath: string) => Promise<void>,
  fileNameBuilder?: (id: number) => string,
) {
  const id = baseTest.info().parallelIndex;
  const fileName = fileNameBuilder ? fileNameBuilder(id) : getFilePath(id);

  try {
    const account = acquireAccount(role);

    // Use autoLogin which handles storage state checking and creation
    const storagePath: string = await autoLogin(
      browser,
      account.username,
      account.password || account.defaultPassword,
      {
        storagePath: fileName,
        workerId: id,
        reuseExisting: true,
      },
    );
    const baseUrl = process.env['BASE_URL'] || '';
    const client = getGlobalApiClient({ baseUrl });
    const apiKey = await getCookie('BravoWeb', storagePath);
    await client.addContext(createApiKeyAuth(CONTEXT_COOKIE, `BravoWeb=${apiKey}`, { headerName: 'cookie' }));
    await client.addContext(createApiKeyAuth(CONTEXT_KEY, `${API_KEY_VALUE}`, { headerName: 'x-api-key' }));
    await use(storagePath);
  } catch (error) {
    handleException(error, `auto sign in with role (${role}) failed`);
  }
}

type PageObject = {
  purchaseOrder: PurchaseOrderPage;
};

const test = baseTest.extend<PageObject, { signInAsAdmin: string }>({
  // Use the same storage state for all tests in this worker.
  storageState: ({ signInAsAdmin }, use) => use(signInAsAdmin),
  // Sign in as Admin before all tests in the worker
  // Use custom filename builder to preserve existing cache pattern
  // for Admin role
  signInAsAdmin: [
    async ({ browser }, use) => {
      // Preserve original filename pattern for Admin to keep existing cache
      await authForRole('Admin', browser, use, id => getFilePath(id));
    },
    { scope: 'worker' },
  ],
  // Page object fixture
  purchaseOrder: async ({}, use) => {
    await use(new PurchaseOrderPage());
  },
});

export { test, expect };
