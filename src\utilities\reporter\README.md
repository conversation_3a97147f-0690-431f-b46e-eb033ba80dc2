# Reporter & Error Handling Utilities

Unified reporting layer: logging, custom expectations, structured test result metadata, and consistent error handling.

## Modules

| File                                                | Purpose                                                                                  |
| --------------------------------------------------- | ---------------------------------------------------------------------------------------- |
| `custom-logger.ts`                                  | Winston logger with masking for sensitive fields (`password`, `token`, `authorization`). |
| `custom-expect.ts`                                  | Extends <PERSON><PERSON>'s expect; integrates logging + masking helpers.                       |
| `monocart-config.ts` / `monocart-columns-config.ts` | Monocart reporting configuration & column definitions.                                   |
| `error-handler.ts`                                  | Centralized helper to enrich & rethrow errors; used by UI/API/db layers.                 |

## Usage

```typescript
import { logger } from '@reporter';
logger.info('Starting test');
```

Assertions:

```typescript
import { expect } from '@reporter';
await expect(locator).toBeVisible();
```

## Error Handling Strategy

Goal: Fail fast with context, mask secrets, preserve diagnostics.

### Layers

1. **Action Level** – Playwright wrapper functions catch low-level errors, add selector + timeout info, then rethrow via `handleException`.
2. **Fixture Level** – Auth or environment setup captures manual screenshots (stored outside auto-cleaned `test-results`).
3. **Global Hooks** – `global-setup` / `global-teardown` log active environment, configuration summaries, and cleanup steps.

### Patterns

- Always call `handleException(error, message)` instead of ad-hoc logging.
- Mask credentials / tokens before logging (logger does this automatically when structured objects are passed).
- On fixture failure, attempt a screenshot to `fixture-failures/` and include its path in the rethrown error message.

### Adding New Context

Extend `error-handler.ts` with:

- Category tagging (e.g., `UI`, `API`, `DB`).
- Retry hint metadata when a failure is known to be transient.
- Optional correlation IDs for distributed tracing (inject via env var if available).

## Logging Conventions

- Use `logger.debug` for verbose traces (selectors, payload fragments).
- Use `logger.info` for high-level lifecycle (auth start, navigation, API request summary).
- Use `logger.warn` for recoverable anomalies (retry attempts, fallback paths).
- Use `logger.error` only when throwing or irrecoverable.

## Extending

- Add transports (e.g., JSON file, console formatting) inside `custom-logger.ts`.
- Add custom matchers in `custom-expect.ts` and update `src/types/custom-expect-types.d.ts` to expose typings.
- Introduce structured error classes if domain-specific enrichment is needed (wrap inside `handleException`).

## FAQ

**Q: Why one README?** Consolidates logging + error guidance to avoid divergence.

**Q: Where are screenshots for setup failures?** Saved under `fixture-failures/` (not auto-deleted) – referenced in failure logs.

**Q: How are secrets masked?** Logger inspects object keys and replaces sensitive values with `***MASKED***` before output.

## Example Matcher Extension (Sketch)

```typescript
// custom-expect.ts
expect.extend({
  async toHaveTrimmedText(locator: Locator, expected: string) {
    const actual = (await locator.textContent())?.trim();
    const pass = actual === expected;
    return {
      pass,
      message: () => `Expected trimmed text ${expected} but got ${actual}`,
    };
  },
});
```

Add type augmentation in `src/types/custom-expect-types.d.ts`.

---

Centralizing these practices ensures consistent, debuggable output across UI, API, and DB layers.
