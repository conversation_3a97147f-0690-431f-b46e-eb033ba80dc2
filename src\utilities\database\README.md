# MongoDB Database Integration - Comprehensive Documentation

## Data Export Configuration Examples

This section contains example configurations for the `npm run init-testdata` command.

### Basic Usage

#### Export all users to JSON file

```bash
npm run init-testdata -- --collection users --output ./tests/test-data/users.json
```

#### Export with query filter

```bash
npm run init-testdata -- --collection users --output ./tests/test-data/active-users.json --query '{"status": "active"}'
```

#### Export with field projection (exclude sensitive data)

```bash
npm run init-testdata -- --collection users --output ./tests/test-data/users-safe.json --projection '{"password": 0, "email": 0}'
```

#### Export with limit

```bash
npm run init-testdata -- --collection users --output ./tests/test-data/sample-users.json --limit 100
```

#### Complex query with multiple filters

```bash
npm run init-testdata -- --collection orders --output ./tests/test-data/recent-orders.json --query '{"createdAt": {"$gte": "2024-01-01"}, "status": "completed"}' --limit 500
```

### Utility Commands

#### Test database connection

```bash
npm run init-testdata -- --test
```

#### List all available collections

```bash
npm run init-testdata -- --list
```

### Common Query Examples

## MongoDB Database Integration - Implementation Summary

### ✅ Successfully Implemented

#### 1. Database Folder Structure

- Created `src/utilities/database/` folder
- Implemented MongoDB client with TypeScript support
- Added proper exports and index files

#### 2. MongoDB Connection Client (`src/utilities/database/mongo-client.ts`)

Features implemented:

- ✅ Connection management with connection pooling
- ✅ Environment-based configuration from `environments/db.env`
- ✅ Export collection data to JSON files
- ✅ Query filtering support
- ✅ Field projection support
- ✅ Document limit support
- ✅ Connection testing
- ✅ Collection listing
- ✅ Document counting
- ✅ Automatic directory creation
- ✅ Proper error handling
- ✅ TypeScript types and interfaces

#### 3. Command Line Interface (`scripts/init-testdata.cjs`)

- ✅ `npm run init-testdata` command implemented
- ✅ Support for all required functionality:
  - Export collections to JSON files
  - Custom output paths
  - Query filtering
  - Field projection
  - Document limits
  - Connection testing
  - Collection listing

#### 4. Environment Configuration

Configuration in `environments/db.env`:

```env
MONGODB_CONNECTION_STRING=mongodb://localhost:27017
MONGODB_DATABASE_NAME=test_automation
MONGODB_TIMEOUT=30000
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2
```
