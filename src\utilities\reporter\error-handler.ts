import { logger } from '@report-helper';

export function getErrorMessage(error: any): string {
  if (error instanceof Error) return error.message;
  else
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return
    return error.toString();
}

/**
 * Checks whether a value is not empty (i.e., not null, undefined, or an empty string).
 * If the value is empty, it throws an error with a custom message indicating the specific field.
 * @param value - The value to check for being non-empty. It could be of any type.
 * @param name - The name of the field or variable being validated. This is used in the error message if the validation fails.
 * @throws {Error} Throws an error if the value is null, undefined, or an empty string.
 */
export function checkNotEmpty(value: any, name: string): void {
  // Check if the value is null, undefined, or an empty string
  if (value === null || value === undefined || value === '') {
    // Throw an error if the value is empty
    throw new Error(`${name} must not be null, undefined, or empty.`);
  }
}

/**
 * Handles errors by logging the error message and rethrowing it.
 * This method is useful for centralizing error handling logic and ensuring consistent logging of errors.
 * If the error is an instance of the built-in `Error` object, its message is logged.
 * If the error is of unknown type, a generic message is logged instead.
 * @param error - The error object that was thrown. This is expected to be of `unknown` type.
 * @param customMessage - A custom message that provides context about where the error occurred.
 * This message is prefixed to the logged error message.
 * @throws {unknown} Rethrows the error after logging it for further handling upstream.
 */
export function handleException(error: unknown, customMessage: string): never {
  if (error instanceof Error) {
    logger.error(`${customMessage}: ${error.message}`, { stack: error.stack });
    throw error; // Re-throw the original Error instance
  }
  logger.error(`${customMessage}: Non-Error thrown`, { value: error });
  raiseError(customMessage);
}

/**
 * Raises a custom error with proper logging and context.
 * This method is useful for intentionally throwing errors in API operations
 * with consistent logging and error formatting.
 * @param message - The error message to display
 * @param context - Optional context object with additional error details
 * @param errorType - Optional error type/category for better error classification
 * @throws {Error} Always throws an error with the provided message
 */
export function raiseError(message: string, context?: Record<string, unknown>, errorType?: string): never {
  const errorMessage = `[${errorType}] ${message}`;

  // Log the error with context
  logger.error(errorMessage, context ? { context } : undefined);

  // Create and throw the error
  const error = new Error(message);
  error.name = errorType ?? 'Custom Error';

  // Add context to error object if provided
  if (context) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    (error as any).context = context;
  }

  throw error;
}

/**
 * Raises a validation error for API requests.
 * @param field - The field that failed validation
 * @param value - The invalid value
 * @param expectedFormat - Expected format description
 * @throws {Error} Always throws a validation error
 */
export function raiseValidationError(field: string, value: unknown, expectedFormat: string): never {
  const context = {
    field,
    value,
    expectedFormat,
  };

  raiseError(`Validation failed for field '${field}': expected ${expectedFormat}`, context, 'ValidationError');
}

/**
 * Raises a timeout error for API operations.
 * @param operation - The operation that timed out
 * @param timeoutMs - Timeout duration in milliseconds
 * @param url - Optional URL context
 * @throws {Error} Always throws a timeout error
 */
export function raiseTimeoutError(operation: string, timeoutMs: number, url?: string): never {
  const context = {
    operation,
    timeoutMs,
    url: url || 'Unknown URL',
  };

  raiseError(`Operation '${operation}' timed out after ${timeoutMs}ms`, context, 'TimeoutError');
}
