// Type definitions for better type safety
interface ColumnStyleMap {
  [key: string]: string;
}

interface Column {
  id: string;
  name?: string;
  align?: string;
  width?: number;
  searchable?: boolean;
  styleMap?: ColumnStyleMap | string;
  formatter?: string | ((value: any, rowItem?: any, columnItem?: any, cellNode?: any) => any);
  invisible?: boolean;
  markdown?: boolean;
}

interface RowItem {
  type?: string;
  [key: string]: any;
}

interface ColumnFormatter {
  getFormatter: (type: string) => (value: any, rowItem?: any, columnItem?: any, cellNode?: any) => any;
}

export const FORMATTED_TAGS = {
  smoke: {
    style: {
      background: '#6F9913',
    },
  },
  sanity: {
    style: 'background:#178F43;',
  },
  critical: {
    background: '#c00',
  },
  fast: {
    background: 'green',
  },
  slow: {
    background: '#d00',
  },
  Positive: {
    style: {
      background: '#0000FF',
    },
  },
  Negative: {
    background: '#FFA500',
  },
} as const;

export const FORMATTED_COLUMNS = (defaultColumns: Column[]): Column[] => {
  // Define new columns upfront for readability
  const newColumns = [
    {
      id: 'owner',
      name: 'Owner',
      align: 'center',
      searchable: true,
      styleMap: { 'font-weight': 'normal' },
    },
    {
      id: 'test_id',
      name: 'Test-Case Id',
      align: 'center',
      searchable: true,
      styleMap: { 'font-weight': 'normal' },
    },
    {
      id: 'tags',
      name: 'Tags',
      width: 150,
      formatter: 'tags',
    },
    {
      id: 'story_id',
      name: 'User Story Id',
      width: 100,
      searchable: true,
      styleMap: 'font-weight:normal;',
    },
  ];

  // Column visibility configuration: true = visible, false = invisible
  const columnVisibilityConfig: { [key: string]: boolean } = {
    type: false,
    retry: false,
    expectedStatus: false,
    location: false,
    outcome: false,
  };

  // Function to set column visibility
  const updateColumnVisibility = (columns: Column[], visibilityConfig: { [key: string]: boolean }): void => {
    columns.forEach((column: Column) => {
      if (column.id && visibilityConfig[column.id] !== undefined) {
        column.invisible = !visibilityConfig[column.id]; // Set invisible based on config
      }
    });
  };

  // Insert new columns before 'duration'
  const updatedColumns: Column[] = [...defaultColumns];
  const index = updatedColumns.findIndex((column: Column) => column.id === 'duration');
  if (index !== -1) {
    updatedColumns.splice(index, 0, ...newColumns);
  }

  // Update visibility based on the configuration
  updateColumnVisibility(updatedColumns, columnVisibilityConfig);

  // Modify the formatter for the duration column
  const durationColumn = updatedColumns.find((column: Column) => column.id === 'duration');
  if (durationColumn) {
    durationColumn.formatter = (value: unknown): string => {
      if (typeof value === 'number' && value) {
        return `<i>${value.toLocaleString()} ms</i>`;
      }
      return String(value);
    };
  }

  // Modify title column formatter
  const titleColumn = updatedColumns.find((column: Column) => column.id === 'title');
  if (titleColumn) {
    titleColumn.formatter = function (
      this: ColumnFormatter,
      value: unknown,
      rowItem?: RowItem,
      columnItem?: Column,
      cellNode?: unknown,
    ): string {
      const previousFormatter = this.getFormatter('tree');
      const v: unknown = previousFormatter(value, rowItem, columnItem, cellNode);
      if (rowItem?.type === 'step') {
        return `${String(v)}<div style="position:absolute;top:0;right:5px;">✔</div>`;
      }
      return String(v);
    };
  }

  // Modify status column formatter
  const statusColumn = updatedColumns.find((column: Column) => column.id === 'status');
  if (statusColumn) {
    statusColumn.formatter = (value: unknown): string => {
      const colorMap: { [key: string]: string } = {
        failed: 'red',
        passed: 'green',
        warning: 'orange',
      };
      const stringValue = String(value);
      const color = colorMap[stringValue] || 'black';
      return `<b style="color:${color};">${stringValue.toUpperCase()}</b>`;
    };
  }

  // Push description column at the end
  updatedColumns.push({
    id: 'description',
    name: 'Description',
    width: 300,
    markdown: true,
    searchable: true,
  });

  return updatedColumns; // Return the updated columns
};
