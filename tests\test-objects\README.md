# tests/test-objects/

Purpose: Encapsulate selectors and behaviors as Page Objects and Components; API models/helpers for API tests. Also houses authentication & role fixtures.

Structure:

- gui/page-objects/pages: page classes with public methods for flows/assertions
- gui/page-objects/components: reusable UI components
- gui/page-objects/fixture-manager.ts: binds Playwright page to framework lifecycle
- api/models and api/helpers: data models and helpers for API tests

Adding new functionality:

- Create a class under pages/ and expose clear methods; no direct locator use in tests
- Components live under components/ and are composed into pages

Guidelines:

- Keep selectors private in page classes
- Use @page-action wrappers for interactions and @page-utils for navigation
- Reuse auth fixtures (`signInAsAdmin`, `signInAsOther`) via `test.use({ storageState: ({ signInAsAdmin }) => signInAsAdmin })`
- Prefer role-bound test objects (`adminTest`) when available instead of repeating `test.use`

Auth Caching Flow:

1. Worker fixture checks for existing storage state file.
2. If missing, performs full login (handles external IdP redirect).
3. Saves JSON after return to app domain for cross-domain cookies.
4. Subsequent tests reuse file; startup is faster.

Dependencies:

- Consumed by specs under tests/tests-management/gui and tests-management/api
