import { request } from '@playwright/test';
import { handleException, logger, raiseError } from '@report-helper';
import {
  type ApiKeyAuthConfig,
  type ApiRequestOptions,
  type AuthConfig,
  AuthType,
  type BasicAuthConfig,
  type BearerAuthConfig,
  type OAuth2AuthConfig,
  type TokenCacheEntry,
} from '@custom-types';

// ===============================================
// Authentication Helper Functions
// ===============================================

/**
 * Create a Bearer token authentication configuration
 */
export function createBearerAuth(
  contextId: string,
  token: string,
  options?: {
    tokenPrefix?: string;
    description?: string;
  },
): BearerAuthConfig {
  return {
    type: AuthType.BEARER,
    contextId,
    token,
    tokenPrefix: options?.tokenPrefix || 'Bearer',
    description: options?.description,
  };
}

/**
 * Create a Basic authentication configuration
 */
export function createBasicAuth(
  contextId: string,
  username: string,
  password: string,
  options?: {
    description?: string;
  },
): BasicAuthConfig {
  return {
    type: AuthType.BASIC,
    contextId,
    username,
    password,
    description: options?.description,
  };
}

/**
 * Create an API Key authentication configuration
 */
export function createApiKeyAuth(
  contextId: string,
  apiKey: string,
  options?: {
    headerName?: string;
    queryParamName?: string;
    description?: string;
  },
): ApiKeyAuthConfig {
  return {
    type: AuthType.API_KEY,
    contextId,
    apiKey,
    headerName: options?.headerName || '',
    queryParamName: options?.queryParamName,
    description: options?.description,
  };
}

/**
 * Create an OAuth2 authentication configuration
 */
export function createOAuth2Auth(
  contextId: string,
  tokenUrl: string,
  clientId: string,
  clientSecret: string,
  options?: {
    scope?: string;
    grantType?: string;
    description?: string;
  },
): OAuth2AuthConfig {
  return {
    type: AuthType.OAUTH2,
    contextId,
    tokenUrl,
    clientId,
    clientSecret,
    scope: options?.scope || '',
    grantType: options?.grantType || 'client_credentials',
    description: options?.description,
  };
}

// ===============================================
// OAuth2 Token Management
// ===============================================

const tokenCache = new Map<string, TokenCacheEntry>();

/**
 * Get or refresh OAuth2 token
 */
async function getOAuth2Token(config: OAuth2AuthConfig): Promise<string> {
  const cacheKey = `${config.contextId}_oauth2`;
  const cached = tokenCache.get(cacheKey);

  // Check if we have a valid cached token (with 5 minute buffer)
  if (cached && isTokenValid(cached, 300)) {
    return cached.token;
  }

  try {
    // Request new token
    const tokenResponse = await requestOAuth2Token(config);

    // Cache the token
    const expiresAt = new Date();
    if (tokenResponse.expires_in) {
      expiresAt.setSeconds(expiresAt.getSeconds() + tokenResponse.expires_in);
    } else {
      // Default to 1 hour if no expiry provided
      expiresAt.setHours(expiresAt.getHours() + 1);
    }

    const cacheEntry: TokenCacheEntry = {
      token: tokenResponse.access_token,
      expiresAt,
      tokenType: tokenResponse.token_type,
    };

    tokenCache.set(cacheKey, cacheEntry);

    return tokenResponse.access_token;
  } catch (error) {
    handleException(error, `[Auth Helper] Failed to get OAuth2 token for context '${config.contextId}'`);
  }
}

/**
 * Request OAuth2 token from server
 */
async function requestOAuth2Token(config: OAuth2AuthConfig): Promise<{
  access_token: string;
  token_type?: string;
  expires_in?: number;
}> {
  const tokenContext = await request.newContext();

  try {
    const response = await tokenContext.post(config.tokenUrl, {
      form: {
        grant_type: config.grantType || 'client_credentials',
        client_id: config.clientId,
        client_secret: config.clientSecret,
        ...(config.scope && { scope: config.scope }),
      },
    });

    if (!response.ok()) {
      throw new Error(`OAuth2 token request failed: ${response.status()} ${response.statusText()}`);
    }

    return (await response.json()) as {
      access_token: string;
      token_type?: string;
      expires_in?: number;
    };
  } finally {
    await tokenContext.dispose();
  }
}

/**
 * Check if token is still valid
 */
function isTokenValid(cached: TokenCacheEntry, bufferSeconds: number): boolean {
  const now = new Date();
  const expiryWithBuffer = new Date(cached.expiresAt.getTime() - bufferSeconds * 1000);
  return now < expiryWithBuffer;
}

// ===============================================
// Authentication Application
// ===============================================

/**
 * Apply authentication to a request based on auth config
 */
export async function applyAuthentication(options: ApiRequestOptions, config: AuthConfig): Promise<ApiRequestOptions> {
  const modifiedOptions = { ...options };

  if (!modifiedOptions.headers) {
    modifiedOptions.headers = {};
  }

  try {
    switch (config.type) {
      case AuthType.BEARER: {
        const tokenPrefix = config.tokenPrefix || 'Bearer';
        modifiedOptions.headers['Authorization'] = `${tokenPrefix} ${config.token}`;
        break;
      }

      case AuthType.BASIC: {
        const credentials = Buffer.from(`${config.username}:${config.password}`).toString('base64');
        modifiedOptions.headers['Authorization'] = `Basic ${credentials}`;
        break;
      }

      case AuthType.API_KEY: {
        const apiKey = config.apiKey ?? '';
        if (!apiKey) RangeError('API Key is missing');
        if (config.headerName) {
          modifiedOptions.headers[config.headerName] = apiKey;
        } else if (config.queryParamName) {
          if (!modifiedOptions.queryParams) {
            modifiedOptions.queryParams = {};
          }
          modifiedOptions.queryParams[config.queryParamName] = apiKey;
        } else {
          modifiedOptions.headers['X-API-Key'] = apiKey;
        }
        break;
      }

      case AuthType.OAUTH2: {
        const token = await getOAuth2Token(config);
        modifiedOptions.headers['Authorization'] = `Bearer ${token}`;
        break;
      }

      default:
        raiseError(`Unsupported authentication type: ${String(config)}`);
    }

    logger.debug(`[Auth Helper] Applied ${config.type} authentication for context '${config.contextId}'`);
    return modifiedOptions;
  } catch (error) {
    handleException(error, `[Auth Helper] Failed to apply authentication for context '${config.contextId}'`);
  }
}

// ===============================================
// Environment Integration
// ===============================================

/**
 * Get base URL from environment variables
 */
export function getBaseUrlFromEnv(): string {
  return process.env['API_BASE_URL'] || process.env['BASE_URL'] || '';
}

/**
 * Get authentication token from environment
 */
export function getTokenFromEnv(envVarName = 'AUTH_TOKEN'): string {
  const token = process.env[envVarName];
  if (!token) {
    throw new Error(`Authentication token not found in environment variable: ${envVarName}`);
  }
  return token;
}

/**
 * Create auth config from environment variables
 */
export function createAuthFromEnv(contextId: string, type: 'bearer' | 'basic' | 'apiKey'): AuthConfig {
  switch (type) {
    case 'bearer': {
      return createBearerAuth(contextId, getTokenFromEnv('AUTH_TOKEN'));
    }

    case 'basic': {
      const username = process.env['AUTH_USERNAME'];
      const password = process.env['AUTH_PASSWORD'];
      if (!username || !password) {
        throw new Error('AUTH_USERNAME and AUTH_PASSWORD environment variables are required for basic auth');
      }
      return createBasicAuth(contextId, username, password);
    }

    case 'apiKey': {
      return createApiKeyAuth(contextId, getTokenFromEnv('API_KEY'));
    }

    default: {
      raiseError(`Unsupported auth type: ${String(type)}`);
    }
  }
}
