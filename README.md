## Bravo Automation Test Framework (Playwright + TypeScript)

Reusable Playwright + TypeScript automation framework with:

- Path aliases (no fragile relative imports)
- Centralized configs & environment loading
- UI / API / DB utilities (layered & logged)
- Custom logging, assertions, and reporting (Monocart + HTML)
- Auth fixtures with cached storage state per role

### Folder Structure Overview

- **configs/**: Centralized configuration for Playwright projects, timeouts, and environment setup/teardown. See [configs/README.md](configs/README.md) for details.
- **environments/**: `.env` files for different environments (dev, staging, release, db). See [environments/README.md](environments/README.md).
- **src/utilities/**: Reusable utilities for UI, API, reporting, and database operations. Subfolders include:
  - **api/**: API helpers and authentication utilities. See [src/utilities/api/README.md](src/utilities/api/README.md).
  - **common/**: Shared helper utilities (e.g., string, date, object utilities). See [src/utilities/common/README.md](src/utilities/common/README.md).
  - **database/**: MongoDB integration utilities. See [src/utilities/database/README.md](src/utilities/database/README.md).
  - **reporter/**: Custom logging, assertions, and reporting utilities. See [src/utilities/reporter/README.md](src/utilities/reporter/README.md).
  - **ui/**: UI utilities layered above Playwright. See [src/utilities/ui/README.md](src/utilities/ui/README.md).
- **src/types/**: Shared TypeScript types and global augmentations. See [src/types/README.md](src/types/README.md).
- **tests/**: Organized test specifications and related data:
  - **test-management/**: GUI and API test specs, and test preparation scripts. See [tests/test-management/README.md](tests/test-management/README.md).
  - **test-objects/**: Page objects, components, and API models/helpers. See [tests/test-objects/README.md](tests/test-objects/README.md).
  - **test-data/**: Static data sets for tests.
- **test-results/**: Generated test reports and screenshots.

### Setup and Usage

#### Prerequisites

- Node.js >= 18 (prefer LTS).

#### Installation

```bash
npm install
```

#### Running Tests

- Run all tests:
  ```bash
  npm test
  ```
- Run GUI tests in Chromium:
  ```bash
  npm run test:chromium
  ```
- Run API tests only:
  ```bash
  npm run test:api
  ```

#### Reports

- Playwright HTML Report: `test-results/html-report/index.html`
- Monocart Report: `test-results/monocart-report/report.html`

#### Switching Environments

Set the `NODE_ENV` variable to select the environment:

```bash
$env:NODE_ENV = 'staging'
```

#### Debugging

Enable Playwright debugging:

```bash
$env:PWDEBUG = '1'
```

### Contribution Guidelines

- Follow the [NAMING_CONVENTIONS.md](NAMING_CONVENTIONS.md) for consistent naming.
- Update relevant README files when adding new utilities or features.

### References

For detailed documentation, refer to the README files in the respective folders:

- [configs/README.md](configs/README.md)
- [environments/README.md](environments/README.md)
- [src/utilities/README.md](src/utilities/README.md)
- [tests/test-management/README.md](tests/test-management/README.md)
- [tests/test-objects/README.md](tests/test-objects/README.md)
- [NAMING_CONVENTIONS.md](NAMING_CONVENTIONS.md)
