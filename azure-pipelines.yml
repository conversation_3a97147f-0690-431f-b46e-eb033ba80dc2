parameters:
  - name: environment
    displayName: Target Environment
    type: string
    default: staging
    values: [dev, staging, release]
  - name: lang
    displayName: Language (locale)
    type: string
    default: us
    values: [us, no]
  - name: initTestData
    displayName: Initialize test data (.test-data) before tests
    type: boolean
    default: false
  - name: testPlanId
    displayName: Azure Test Plan ID (optional)
    type: string
    default: '40950'
  - name: azureOrganization
    displayName: Azure DevOps Organization (short name, no https)
    type: string
    default: 'upheads'
  - name: azureProject
    displayName: Azure DevOps Project
    type: string
    default: 'UpTime'

trigger:
  branches:
    include:
      - main
      - dev
      - release/*
  tags:
    include:
      - dev
      - staging
      - prod

pr: none

variables:
  group: BravoPlaywright 
  NODE_ENV: ${{ parameters.environment }}
  TEST_LANG: ${{ parameters.lang }}
  INIT_TEST_DATA: ${{ parameters.initTestData }}
  TEST_PLAN_ID: ${{ parameters.testPlanId }}
  AZURE_ORG: ${{ parameters.azureOrganization }}
  AZURE_PROJECT: ${{ parameters.azureProject }}
  IS_PIPELINE: 'true'

pool:
  name: 'LinuxAgents'

stages:
  - stage: Test
    displayName: Test Chromium
    jobs:
      - job: chromium
        displayName: Chromium Browser Tests
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '24.8'
            displayName: Use Node.js 24.8 install deps
          - script: |
              echo "Environment: $(NODE_ENV)"
              echo "Language: $(TEST_LANG)"
              npm ci
              npx playwright install
            displayName: Install deps and browsers
          - script: |
              if [ ${{ parameters.initTestData }} = true ]; then
                echo "Init data new as request"
              fi
            displayName: init data as user request
          - script: |
              echo "Running chromium on $(NODE_ENV) LANG=$(TEST_LANG)"
              export LANG=$(TEST_LANG)
              npx playwright test --project=chromium
            displayName: Run Chromium tests
            env:
              ADMIN_PWD: $(ADMIN_PWD)
              BRAVO_API_KEY: $(BRAVO_API_KEY)
          - task: PublishTestResults@2
            displayName: Publish JUnit test results
            condition: succeededOrFailed()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'test-results/$(NODE_ENV)/junit/results.xml'
              mergeTestResults: true
              testRunTitle: 'Playwright Chromium - $(NODE_ENV)'
              failTaskOnFailedTests: false
          - task: PublishPipelineArtifact@1
            condition: succeededOrFailed()
            inputs:
              targetPath: '$(System.DefaultWorkingDirectory)/test-results'
              artifact: 'test-results-chromium'
            displayName: Publish test results artifact
          - script: |
              if [ -n "$(TEST_PLAN_ID)" ] && [ "$(TEST_PLAN_ID)" != "" ]; then
                echo "Test Plan ID provided: $(TEST_PLAN_ID)"
                echo "(Placeholder) Implement REST API call to associate results with Test Plan $(TEST_PLAN_ID)."
                echo "Example curl (requires secret AZURE_DEVOPS_PAT):"
                echo "curl -u :\$AZURE_DEVOPS_PAT -X POST \"https://dev.azure.com/$(AZURE_ORG)/$(AZURE_PROJECT)/_apis/test/runs?api-version=7.1-preview.1\" \\"
                echo "  -H 'Content-Type: application/json' \\"
                echo "  -d '{\"name\":\"Playwright Chromium $(Build.BuildNumber)\",\"plan\":{\"id\":$(TEST_PLAN_ID)}}'"
              else
                echo "No Test Plan ID supplied; skipping linkage step."
              fi
            displayName: Link test run to Azure Test Plan (placeholder)
            env:
              AZURE_DEVOPS_PAT: $(AZURE_DEVOPS_PAT)
              TEST_PLAN_ID: $(TEST_PLAN_ID)
              AZURE_ORG: $(AZURE_ORG)
              AZURE_PROJECT: $(AZURE_PROJECT)
            condition: succeededOrFailed()
