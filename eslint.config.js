import js from '@eslint/js';
import ts from 'typescript-eslint';
import playwright from 'eslint-plugin-playwright';

export default ts.config(
  {
    // Note: there should be no other properties in this object
    ignores: ['scripts/**/*', 'eslint.config.js', 'node_modules/**/*', 'dist/**/*', 'build/**/*', '*.cjs', '*.mjs'],
  },

  // Core ESLint recommended
  js.configs.recommended,

  // TypeScript recommended (syntax + type-aware)
  ...ts.configs.recommended,
  ...ts.configs.recommendedTypeChecked,

  // Add language options for type checking
  {
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },

  // Project-wide customizations
  {
    rules: {
      'no-unused-vars': 'off', // use TS-aware rule instead
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-unsafe-assignment': 'off', //  switch to "error" later
      '@typescript-eslint/no-unsafe-member-access': 'warn', // switch to "error" later
      '@typescript-eslint/no-explicit-any': 'off', // switch to "error" later
      '@typescript-eslint/no-unsafe-function-type': 'off', // enable after cleanup
      'prefer-const': 'off',
      'no-empty-pattern': 'off',
    },
  },

  // Playwright tests
  {
    files: ['tests/**/*.ts', '**/*.spec.ts', '**/*.test.ts', 'tests/test-management/**'],
    ...playwright.configs['flat/recommended'],
    rules: {
      // Project-specific tweaks
      'playwright/no-commented-out-tests': 'warn',
      'playwright/no-duplicate-hooks': 'error',
      'playwright/no-get-by-title': 'warn',
      'playwright/no-nth-methods': 'error',
      'playwright/no-restricted-matchers': 'warn',
      'playwright/prefer-comparison-matcher': 'warn',
      'playwright/prefer-equality-matcher': 'warn',
      'playwright/prefer-hooks-in-order': 'warn',
      'playwright/prefer-hooks-on-top': 'warn',

      // Loosen restrictions for authoring flexibility
      'playwright/expect-expect': 'off',
      'playwright/prefer-lowercase-title': 'off',
    },
  },
);
