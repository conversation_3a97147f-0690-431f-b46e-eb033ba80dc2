// ===============================================
// Simplified Multi-Context API System
// ===============================================

// Core Classes
import process from 'node:process';
import { ApiClient } from './api-client.js';
import type { ApiClientConfig, ApiRequestOptions } from '@custom-types';

export { ApiClient } from './api-client.js';
export { ContextManager, getGlobalContextManager, resetGlobalContextManager } from './context-manager.js';

// Authentication Helper Functions
export {
  createBearerAuth,
  createBasicAuth,
  createApiKeyAuth,
  createOAuth2Auth,
  applyAuthentication,
  getBaseUrlFromEnv,
  getTokenFromEnv,
  createAuthFromEnv,
} from './auth-helper.js';

// ===============================================
// Convenience Functions
// ===============================================

let globalApiClient: ApiClient | null = null;

/**
 * Get or create the global API client instance
 */
export function getGlobalApiClient(config?: Partial<ApiClientConfig>): ApiClient {
  if (!globalApiClient) {
    globalApiClient = new ApiClient(config);
  }
  return globalApiClient;
}

/**
 * Reset the global API client instance
 */
export async function resetGlobalApiClient(): Promise<void> {
  if (globalApiClient) {
    await globalApiClient.dispose();
    globalApiClient = null;
  }
}

/**
 * Quick HTTP method helpers using global client
 */
export async function get(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>) {
  return getGlobalApiClient().get(url, options);
}

export async function post(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>) {
  return getGlobalApiClient().post(url, options);
}

export async function put(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>) {
  return getGlobalApiClient().put(url, options);
}

export async function patch(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>) {
  return getGlobalApiClient().patch(url, options);
}

export async function del(url: string, options?: Omit<ApiRequestOptions, 'method' | 'url'>) {
  return getGlobalApiClient().delete(url, options);
}

export const API_KEY_VALUE: string = process.env[process.env['API_ENV_KEY'] || ''] || '';
export const CONTEXT_KEY: string = 'api-key';
export const CONTEXT_COOKIE: string = 'cookie';
