import { getLocatorByRole } from '@locator-helper';
import { expectElementToBeVisible, expectElementToContainText } from '@ui-action';
import { BIG_TIMEOUT } from '@global-timeout';

/**
 * Expect toast message contains the given text
 * @param toastText The text to be contained in the toast message
 */
export async function expectToastMessageContains(toastText: string) {
  const toast = getLocatorByRole('alert');
  await expectElementToBeVisible(toast, { timeout: BIG_TIMEOUT });
  await expectElementToContainText(toast, toastText);
}
