import { CONTEXT_COOKIE, get, post } from '@api-helper';
import { expect } from '@fixture-manager';
import { handleException } from '@report-helper';

const endPoint = 'ProCus';

/**
 * Get contacts by company id
 * @param companyId - Company ID
 * @returns Array of contacts
 */
export async function getContacts(companyId: string): Promise<any[]> {
  try {
    const response = await get(`/${endPoint}/GetContacts`, {
      contextId: CONTEXT_COOKIE,
      queryParams: {
        id: companyId,
        sord: 'asc',
        page: 1,
        rows: 1000,
      },
    });
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    const items: [] = data.rows || [];
    return items;
  } catch (error) {
    handleException(error, 'Error while retrieving get approve flows');
  }
}

/**
 * Get contract with email or without email
 * @param companyId - Company ID
 * @param hasEmail - true to get contract with email, false to get contract without email
 * @returns Contact object or undefined
 * @throws Error if there is an issue with the API call
 * @example
 * Contact object return : {
 *   CustomerPersonId: "ecff2115-cbe8-437c-92d0-8b6b168328e8",
 *   LastName: "Nguyen",
 *   Name: "Lam Nguyen",
 *   NameAndEmail: "Lam Nguyen (<EMAIL>)",
 *   Email: "<EMAIL>"
 * }
 */
export async function getContract(companyId: string, hasEmail: boolean = true): Promise<any> {
  try {
    const contacts = await getContacts(companyId);
    if (hasEmail) {
      return contacts.find((contact: { Email: string }) => contact.Email && contact.Email.trim() !== '');
    } else {
      return contacts.find(
        (contact: { Email?: string | null | undefined }) =>
          contact.Email === undefined || contact.Email === null || contact.Email.trim() === '',
      );
    }
  } catch (error) {
    handleException(error, `Error while retrieving get contract with ${hasEmail ? 'email' : 'no email'}`);
  }
}

/**
 * Create contact for a company
 * @param companyId - Company ID
 * @param firstName - First name
 * @param lastName - Last name
 * @param email - Email (optional)
 * @returns Contact object - responseBody?.Data?.Contact
 * @throws Error if there is an issue with the API call
 * @example
 * Contact object return : {
 *   IsError: false,
 *   Object: [],
 *   Data:{
 *      Success: true,
 *      CustomerId: "3977a93b-845b-4e06-9569-01be5068adbd",
 *      Contact: {
 *        CustomerPersonId: "ecff2115-cbe8-437c-92d0-8b6b168328e8",
 *        CustomerId: "3977a93b-845b-4e06-9569-01be5068adbd",
 *        LastName: "Nguyen",
 *        FirstName: "Nguyen",
 *        FullName: "Lam Nguyen",
 *        Email: "<EMAIL>"
 *      }
 *   }
 * }
 */
export async function createContact(
  companyId: string,
  firstName: string,
  lastName: string,
  email?: string,
): Promise<any> {
  try {
    const response = await post(`/${endPoint}/EditContact`, {
      contextId: CONTEXT_COOKIE,
      form: {
        CustomerId: companyId,
        title: `QC`,
        FirstName: firstName,
        LastName: lastName,
        Email: email ? email : '',
        InfoEmail: true,
        OperationMessages: true,
        Inactive: false,
        IsLocked: false,
        SMSAlert: false,
      },
    });
    expect(response.ok()).toBeTruthy();
    const data: any = await response.json();
    return data?.Data?.Contact;
  } catch (error) {
    handleException(error, 'Error while creating contact');
  }
}
