// Minimal Account Manager
// Provides a simple API to retrieve an account from tests/test-data/users.json
// by role (default = 'Admin') and optional id. Environment inferred automatically.

import { logger, raiseError, handleException } from '@report-helper';
import { loadJson } from '@common-helper';

export type UserRole = 'Admin' | 'Other' | 'Normal';

// Unified Account model (used for both raw JSON data and returned object)
// password is resolved on retrieval (from env override or defaultPassword)
export interface Account {
  displayName: string;
  username: string;
  email: string;
  passwordEnvKey: string; // key to look up environment override
  defaultPassword: string; // fallback password if env var is not set
  role: UserRole;
  id: string;
  password?: string; // resolved password (added at retrieval time)
}

interface UsersData {
  [environment: string]: {
    [role: string]: Account[];
  };
}

// Internal cache
let cachedUsersData: UsersData | null = null;
let resolvedEnv: string | null = null;

function getUsersData(): UsersData {
  if (!cachedUsersData) {
    cachedUsersData = loadJson<UsersData>('users.json');
  }
  return cachedUsersData;
}

function resolveEnvironment(): string {
  if (resolvedEnv) return resolvedEnv;
  const usersData = getUsersData();
  const candidates = [process.env['TEST_ENV'], process.env['NODE_ENV'], 'dev'];
  for (const c of candidates) {
    if (c && usersData[c]) {
      resolvedEnv = c;
      logger.info(`Account environment resolved to '${resolvedEnv}'`);
      return resolvedEnv;
    }
  }
  const first = Object.keys(usersData)[0];
  if (!first) raiseError('users.json has no environments defined');
  resolvedEnv = first;
  logger.warn(`Falling back to first environment '${resolvedEnv}'`);
  return resolvedEnv;
}

function resolvePassword(account: Account): string {
  return process.env[account.passwordEnvKey] || account.defaultPassword;
}

/**
 * Get an account by role (default 'Admin') and optional id from users.json.
 * Environment is auto-detected via TEST_ENV -> NODE_ENV -> 'dev' -> first key.
 * Returns a new object so original cached data is not mutated.
 * @param role Role in users.json (default 'Admin')
 * @param id Optional specific account id.
 */
export function getAccount(role: UserRole = 'Admin', id?: string): Account {
  try {
    const data = getUsersData();
    const env = resolveEnvironment();
    const envBlock = data[env];
    if (!envBlock) raiseError(`Environment '${env}' not found in users.json`);
    const roleBlock = envBlock[role];
    if (!roleBlock || roleBlock.length === 0) {
      raiseError(`No accounts for role '${role}' in environment '${env}'`);
    }
    const source = id ? roleBlock.find(r => r.id === id) : roleBlock[0];
    if (!source) raiseError(`Account id '${id}' not found for role '${role}' in environment '${env}'`);
    const resolved: Account = { ...source, password: resolvePassword(source) };
    logger.info(`Retrieved account: env=${env}, role=${resolved.role}, id=${resolved.id}, user=${resolved.username}`);
    return resolved;
  } catch (error) {
    handleException(error, 'Failed to get account');
  }
}

/** Backward compatible alias */
export function acquireAccount(roleOrId?: UserRole, idMaybe?: string): Account {
  if (!roleOrId) return getAccount('Admin');
  return getAccount(roleOrId, idMaybe);
}
