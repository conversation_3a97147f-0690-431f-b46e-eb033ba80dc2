import { readFileSync } from 'node:fs';
import path from 'node:path';
import { handleException, logger } from '@report-helper';

// Simple in-memory cache
const jsonCache = new Map<string, unknown>();

export interface LoadJsonOptions {
  cache?: boolean; // default true
  baseDir?: string; // override base directory (defaults to tests/test-data)
  silent?: boolean; // suppress info log on success
}

/**
 * Loads JSON test data from tests/test-data by filename.
 * @param fileName File name within tests/test-data (e.g., 'users.json')
 * @param options Optional loader behavior overrides
 * @returns Parsed JSON as type T
 * @example
 * const users = loadJson<User[]>('users.json');
 */
export function loadJson<T = unknown>(fileName: string, options?: LoadJsonOptions): T {
  if (!fileName) {
    throw new Error('loadJson: fileName must be a non-empty string');
  }
  const { cache = true, baseDir, silent = false } = options || {};
  const full = path.resolve(process.cwd(), baseDir ?? 'tests/test-data', fileName);

  if (cache && jsonCache.has(full)) {
    return jsonCache.get(full) as T;
  }

  try {
    const raw = readFileSync(full, 'utf-8');
    const parsed = JSON.parse(raw) as T;
    if (cache) jsonCache.set(full, parsed);
    if (!silent) logger.info(`Loaded test data: ${fileName}`);
    return parsed;
  } catch (err) {
    // handleException will rethrow
    handleException(err, `Failed to load test data ${fileName}`);
  }
}

/**
 * Clears the in-memory JSON cache (useful for tests that mutate data files at runtime).
 */
export function clearJsonCache(): void {
  jsonCache.clear();
  logger.debug('JSON data cache cleared');
}
