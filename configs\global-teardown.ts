// import type { FullConfig } from '@playwright/test';

// export default async function globalTeardown(_config: FullConfig): Promise<void> {
//   // Cleanup shared resources and artifacts if needed
//   // @ts-expect-error - Ignore the next line TypeScript errors about 'any' type
//   if (global.browser) {
//     // @ts-expect-error - Ignore the next line TypeScript errors about 'any' type
//     await global.browser.close();
//   }
//   // e.g., close db connections, remove temp files
// }
