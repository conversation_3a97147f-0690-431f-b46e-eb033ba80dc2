import { CONTEXT_COOKIE, get, post } from '@api-helper';
import { APIResponse } from '@playwright/test';
import { expect } from '@fixture-manager';
import { handleException } from '@report-helper';

const endPoint = 'ApprovalFlow';

export async function getApproveFlow(flowName: string): Promise<any> {
  try {
    const response = await get(`/${endPoint}/GetApprovalFlows`, {
      contextId: CONTEXT_COOKIE,
      queryParams: {
        sord: 'asc',
        page: 1,
        rows: 1000,
      },
    });
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    const items: [] = data.rows || [];
    return items?.find((item: { FlowName: string }) => item.FlowName === flowName);
  } catch (error) {
    handleException(error, 'Error while retrieving get approve flows');
  }
}

export async function getStepByFlowId(flowId: string): Promise<any[]> {
  try {
    const response = await get(`/${endPoint}/GetStepsByApprovalFlowId`, {
      contextId: CONTEXT_COOKIE,
      queryParams: {
        approvalFlowId: flowId,
        rows: 50,
        page: 1,
        sord: 'asc',
      },
    });
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    const items: any[] = data.rows || [];
    return items;
  } catch (error) {
    handleException(error, 'Error while retrieving get approve steps');
  }
}

export async function createApproveFlow(flowName: string, description: string = ''): Promise<string> {
  try {
    const response = await post(`/${endPoint}/Edit`, {
      contextId: CONTEXT_COOKIE,
      body: {
        ApprovalFlowId: '',
        Description: description,
        FlowName: flowName,
        PayrollAndExpense: false,
        TypeId: 1,
      },
    });
    expect(response.ok()).toBeTruthy();
    const data: any = await response.json();
    const id = data?.Data?.ApprovalFlowId;
    return id !== undefined ? String(id) : '';
  } catch (error) {
    handleException(error, 'Error while creating approve flows');
  }
}

export async function addApproveStep(flowId: string, personId: string): Promise<APIResponse> {
  try {
    const formData = new FormData();
    formData.append('ApprovalStepId', '');
    formData.append('ApprovalFlowId', flowId);
    formData.append('PersonId', personId);

    const response = await post(`/${endPoint}/EditApprovalStep`, {
      contextId: CONTEXT_COOKIE,
      form: formData,
    });
    expect(response.ok()).toBeTruthy();
    return response;
  } catch (error) {
    handleException(error, 'Error while adding approve step');
  }
}
