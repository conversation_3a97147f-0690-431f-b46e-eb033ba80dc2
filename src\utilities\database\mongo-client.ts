import { MongoClient, Db, Collection } from 'mongodb';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { handleException, logger, raiseError } from '@report-helper';

// Load environment variables from db.env
dotenv.config({ path: path.resolve(process.cwd(), 'environments/db.env') });

export interface DatabaseConfig {
  connectionString: string;
  databaseName: string;
  timeout: number;
  maxPoolSize: number;
  minPoolSize: number;
}

export interface ExportOptions {
  collectionName: string;
  outputPath: string;
  query?: object;
  projection?: object;
  limit?: number;
}

export class MongoDbClient {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private config: DatabaseConfig;

  constructor() {
    this.config = {
      connectionString: process.env['MONGODB_CONNECTION_STRING'] || 'mongodb://localhost:27017',
      databaseName: process.env['MONGODB_DATABASE_NAME'] || 'BravoAuto',
      timeout: parseInt(process.env['MONGODB_TIMEOUT'] || '30000'),
      maxPoolSize: parseInt(process.env['MONGODB_MAX_POOL_SIZE'] || '10'),
      minPoolSize: parseInt(process.env['MONGODB_MIN_POOL_SIZE'] || '2'),
    };
  }

  /**
   * Connect to MongoDB
   */
  async connect(): Promise<void> {
    try {
      this.client = new MongoClient(this.config.connectionString, {
        serverSelectionTimeoutMS: this.config.timeout,
        maxPoolSize: this.config.maxPoolSize,
        minPoolSize: this.config.minPoolSize,
      });

      await this.client.connect();
      this.db = this.client.db(this.config.databaseName);
      logger.info(`Connected to MongoDB database: ${this.config.databaseName}`);
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      logger.info('Disconnected from MongoDB');
    }
  }

  /**
   * Get a collection from the database
   */
  getCollection(collectionName: string): Collection {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db.collection(collectionName);
  }

  /**
   * Fetch data from a collection and save as JSON file
   */
  async exportCollectionToJson(options: ExportOptions): Promise<void> {
    try {
      if (!this.db) {
        raiseError('Database not connected. Call connect() first.');
      }

      const collection = this.getCollection(options.collectionName);

      // Build query options
      const queryOptions: Record<string, unknown> = {};
      if (options.projection) {
        queryOptions['projection'] = options.projection;
      }
      if (options.limit) {
        queryOptions['limit'] = options.limit;
      }

      // Fetch data
      logger.info(`Fetching data from collection: ${options.collectionName}`);
      const cursor = collection.find(options.query || {}, queryOptions);
      const data = await cursor.toArray();

      logger.info(`Found ${data.length} documents`);

      // Ensure output directory exists
      const outputDir = path.dirname(options.outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        logger.info(`Created directory: ${outputDir}`);
      }

      // Write data to JSON file
      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(options.outputPath, jsonData, 'utf8');

      logger.info(`Data exported successfully to: ${options.outputPath}`);
      logger.info(`File size: ${Math.round(jsonData.length / 1024)} KB`);
    } catch (error) {
      logger.error('Failed to export collection data:', error);
      throw error;
    }
  }

  /**
   * Test the database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.connect();

      // Ping the database
      await this.db!.admin().ping();
      logger.info('Database connection test successful');

      await this.disconnect();
      return true;
    } catch (error) {
      logger.error('Database connection test failed:', error);
      return false;
    }
  }

  /**
   * List all collections in the database
   */
  async listCollections(): Promise<string[]> {
    if (!this.db) {
      raiseError('Database not connected. Call connect() first.');
    }

    try {
      const collections = await this.db.listCollections().toArray();
      return collections.map(col => col.name);
    } catch (error) {
      handleException(error, 'Failed to list collections:');
    }
  }

  /**
   * Get document count for a collection
   */
  async getCollectionCount(collectionName: string, query: object = {}): Promise<number> {
    if (!this.db) {
      raiseError('Database not connected. Call connect() first.');
    }

    try {
      const collection = this.getCollection(collectionName);
      return await collection.countDocuments(query);
    } catch (error) {
      handleException(error, `Failed to get count for collection ${collectionName}:`);
    }
  }
}

export default MongoDbClient;
