# Simplified Multi-Context API Client

A streamlined API client for Playwright TypeScript automation framework that supports multiple authentication contexts with comprehensive logging.

## Overview

The Simplified Multi-Context API Client is designed for Playwright automation testing scenarios where you need to:

- **Test with multiple user roles** simultaneously
- **Maintain session isolation** between different test scenarios
- **Log comprehensive request/response data** for debugging
- **Handle different authentication methods** (Bearer, Basic, API Key, OAuth2)
- **Rotate between contexts** automatically using simple round-robin

## Features

### 🔐 **Multi-Context Authentication**

- **Bearer Token Authentication** with custom prefixes
- **Basic Authentication** for legacy systems
- **API Key Authentication** (header or query parameter)
- **OAuth2 Authentication** with automatic token refresh
- **Session Isolation** - each context maintains its own session state

### 🔄 **Context Management**

- **Simple Round-Robin Rotation** between active contexts
- **Context Health Monitoring** and management
- **Dynamic Context Addition/Removal** during test execution

### 📝 **Comprehensive Logging**

- **Request Logging**: URL, method, headers, query params, body, timestamp
- **Response Logging**: Status, duration, headers, body (with intelligent truncation)
- **Large Response Handling**: Automatic attachment for responses > 1000 characters
- **Integration**: Uses existing `@report-helper` logger
- **No Sensitive Data Masking**: All data logged as-is for debugging

## Quick Start

### Simple Usage (Global Client)

```typescript
import { getGlobalApiClient, createBearerAuth } from '@api-helper';

// Get the global client and add a context
const client = getGlobalApiClient({ baseUrl: 'https://api.example.com' });
await client.addContext(createBearerAuth('user1', 'your-bearer-token'));

// Make requests - context is automatically selected
const users = await client.get('/users');
const newUser = await client.post('/users', {
  body: { name: 'John', email: '<EMAIL>' },
});
```

### Multi-Context Usage

```typescript
import { ApiClient, createBearerAuth, createBasicAuth } from '@api-helper';

const client = new ApiClient({
  baseUrl: 'https://api.example.com',
});

// Add multiple contexts
await client.addContext(createBearerAuth('admin', 'admin-token'));
await client.addContext(createBasicAuth('user', 'user123', 'password123'));

// Make requests - contexts rotate automatically
const adminData = await client.get('/admin/users');
const userData = await client.get('/user/profile');

// Or specify a specific context
const specificData = await client.get('/data', { contextId: 'admin' });
```

## Basic Usage

### Creating Authentication Contexts

```typescript
import { createBearerAuth, createBasicAuth, createApiKeyAuth, createOAuth2Auth } from '@api-helper';

// Bearer Token
const bearerAuth = createBearerAuth('user1', 'your-token-here');

// Basic Authentication
const basicAuth = createBasicAuth('user2', 'username', 'password');

// API Key (header)
const apiKeyAuth = createApiKeyAuth('service1', 'your-api-key', {
  headerName: 'X-API-Key',
});

// OAuth2
const oauth2Auth = createOAuth2Auth('oauth-service', 'https://auth.example.com/token', 'client-id', 'client-secret');
```

### Making Requests

```typescript
import { ApiClient } from '@api-helper';

const client = new ApiClient({ baseUrl: 'https://api.example.com' });

// Add contexts
await client.addContext(bearerAuth);
await client.addContext(basicAuth);

// HTTP Methods
const getResponse = await client.get('/users');
const postResponse = await client.post('/users', {
  body: { name: 'John', email: '<EMAIL>' },
  headers: { 'Content-Type': 'application/json' },
});

// With query parameters
const searchResponse = await client.get('/users', {
  queryParams: { search: 'john', limit: 10 },
});

// With specific context
const adminResponse = await client.get('/admin/settings', {
  contextId: 'admin',
});
```

## Authentication Types

### Bearer Token

```typescript
const config = createBearerAuth('context-id', 'your-token', {
  tokenPrefix: 'Bearer', // Optional, defaults to 'Bearer'
  description: 'Admin user token',
});
```

### Basic Authentication

```typescript
const config = createBasicAuth('context-id', 'username', 'password', {
  description: 'Legacy system auth',
});
```

### API Key

```typescript
// Header-based API Key
const headerConfig = createApiKeyAuth('context-id', 'your-api-key', {
  headerName: 'X-API-Key',
});

// Query parameter-based API Key
const queryConfig = createApiKeyAuth('context-id', 'your-api-key', {
  queryParamName: 'api_key',
});
```

### OAuth2

```typescript
const config = createOAuth2Auth('context-id', 'https://auth.example.com/token', 'client-id', 'client-secret', {
  scope: 'read write',
  grantType: 'client_credentials',
});
```

## Logging

The API client provides comprehensive logging for all requests and responses:

### Request Logging

- Full URL (including base URL and endpoint)
- HTTP Method (GET, POST, PUT, DELETE, etc.)
- Headers (all headers logged without masking)
- Query parameters (if present)
- Request body (if present)
- Timestamp when request was initiated
- Context ID used for the request

### Response Logging

- Response URL (in case of redirects)
- HTTP status code and status text
- Response time/duration in milliseconds
- Response headers
- Response body with intelligent handling:
  - If under 1000 characters: logged in full
  - If over 1000 characters: first 500 characters + "...[truncated]" + full response attached using `test.info().attach()`
- Context ID used for the request

### Log Examples

```
[API Request] GET https://api.example.com/users [Context: user1]
[API Response] ✅ 200 OK (245ms) [Context: user1]
[API Response] Response body truncated. Full response attached: /path/to/attachment
```

## Best Practices

1. **Context Naming**: Use descriptive context IDs that reflect the user role or purpose
2. **Resource Cleanup**: Always call `dispose()` when done with the client
3. **Error Handling**: Wrap API calls in try-catch blocks
4. **Environment Variables**: Use environment variables for sensitive data like tokens
5. **Base URL Configuration**: Set base URLs at the client level rather than in each request

## Troubleshooting

### Common Issues

**No active contexts available**

- Ensure you've added at least one context before making requests
- Check that contexts are active using `getActiveContexts()`

**Authentication failures**

- Verify your tokens/credentials are correct
- Check that the authentication type matches your API requirements

**Request timeouts**

- Increase the timeout in request options or client configuration
- Check network connectivity to the API endpoint

**Large response handling**

- Large responses (>1000 chars) are automatically truncated in logs
- Full responses are attached to test results for debugging

---

## Migration from Previous Implementation

The simplified API maintains backward compatibility with the existing `api-helper.ts` functions. You can gradually migrate to the new multi-context system while keeping existing code functional.
