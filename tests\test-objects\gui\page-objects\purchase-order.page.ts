import { gotoURL, reloadPage, waitForPageLoadState, waitForUrl } from '@page-utils';
import * as process from 'node:process';
import { button, dialogTitle, langPack, tabName } from '@test-data/handlers/language-packs.handler';
import {
  click,
  expectElementToBeHidden,
  expectElementToBeVisible,
  expectElementToContainText,
  expectElementToHaveText,
  expectElementToHaveValue,
  fill,
  getInputValue,
  getLocator,
  getLocatorByRole,
  getText,
  isElementVisible,
  pressKey,
  releaseFocus,
  sendKeys,
  waitForSomeTime,
} from '@ui-action';
import { INSTANT_TIMEOUT, MAX_TIMEOUT, SMALL_TIMEOUT, STANDARD_TIMEOUT } from '@global-timeout';
import { expandRow, verifyRowData } from '@components/grid.func';

const baseUrl = process.env['BASE_URL'] || '';
const endpoint = 'logistics/purchase-orders';

const purchaseOrderEles = {
  supplier: {
    input: '[formcontrolname="supplierId"] input[role="combobox"]',
    value: (value: string) => `[formcontrolname="supplierId"] >> text="${value}"`,
  },
  department: {
    input: '[formcontrolname="departmentId"] input[role="combobox"]',
    value: (value: string) => `[formcontrolname="departmentId"] >> text="${value}"`,
  },
  internalNoteInput: '[formcontrolname="internalNote"]',
  currency: {
    input: '[formcontrolname="currencyId"] input[role="combobox"]',
    value: (value: string) => getLocator(`[formcontrolname="currencyId"]`).locator('span', { hasText: value }),
  },
  statusValue: 'input[formcontrolname="statusDescription"]',
  buttonOnScreen: (name: string, exact: boolean = false) =>
    getLocatorByRole('button', {
      name: name,
      exact: exact,
    }),
  button: {
    sendToApprove: '[translate="PurchaseOrder.SendToApprove"]',
    sendToSupplier: '[translate="PurchaseOrder.SendToSupplier"]',
    approve: '[translate="PurchaseOrder.Approve"]',
    save: '[translate="Common.Save"]',
    add: '[translate="Common.Add"]',
  },
  sendToApproveDialog: {
    title: () => getLocatorByRole('dialog').getByRole('heading', { name: button().sendToApprove }),
    flowInput: () => getLocatorByRole('dialog').getByRole('combobox'),
  },
  option: (name: string, exact: boolean = false) =>
    getLocatorByRole('option', {
      name: name,
      exact: exact,
    }),
  tabs: {
    orderLines: `nav >> text=${tabName().orderLines}`,
  },
  reference: {
    supplierContact: {
      input: '[formcontrolname="yourReferenceId"] input[role="combobox"]',
      value: (value: string) => `[formcontrolname="yourReferenceId"] >> text="${value}"`,
      clearButton: '[formcontrolname="yourReferenceId"] [role="button"][title="Clear all"]',
    },
  },
};
const addNewLineDialog = {
  title: (title: string) => getLocatorByRole('dialog').getByRole('heading', { name: title }),
  productSku: {
    input: '[formcontrolname="skuId"] input[role="combobox"]',
    value: (value: string) => `[formcontrolname="skuId"] >> text="${value}"`,
  },
  lineText: {
    input: '[formcontrolname="lineText"]',
  },
  quantity: {
    input: 'input[formcontrolname="quantity"]',
  },
  purchasePrice: {
    input: 'input[formcontrolname="purchasePrice"]',
  },
  option: (name: string, exact: boolean = false) =>
    getLocatorByRole('dialog').getByRole('option', {
      name: name,
      exact: exact,
    }),
  buttonOnScreen: (name: string, exact: boolean = false) =>
    getLocatorByRole('dialog').getByRole('button', {
      name: name,
      exact: exact,
    }),
};
const sendToSupplierDialog = {
  title: () =>
    getLocatorByRole('alertdialog', { name: langPack().isEnglish ? 'Action not allowed' : 'Handling ikke tillatt' }),
  content: (value: string) => getLocatorByRole('alertdialog').filter({ hasText: value }),
  close: () => getLocatorByRole('alertdialog').getByRole('button', { name: button().close }),
};
export default class PurchaseOrderPage {
  /**
   * Opens the Purchase Orders page via url.
   */
  async open(): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}`);
  }

  /**
   * Clicks the "Send to Approve" button and waits for the approval dialog to appear.
   */
  async clickSendToApprove() {
    await click(purchaseOrderEles.button.sendToApprove);
    await waitForPageLoadState();
    await purchaseOrderEles.sendToApproveDialog.title().waitFor({ state: 'visible' });
  }

  /**
   * Clicks the "Approve" button and waits for PO page update status.
   */
  async clickApprove() {
    await click(purchaseOrderEles.button.approve);
    await waitForPageLoadState();
  }

  /**
   * Clicks the "Save" button to save the purchase order.
   * @param expectedStatusAfterSaved - Optional expected status text to verify after saving.
   */
  async clickSave(expectedStatusAfterSaved?: string) {
    await click(purchaseOrderEles.button.save);
    await waitForPageLoadState({ waitUntil: 'networkidle' });
    if (expectedStatusAfterSaved)
      await expectElementToHaveValue(purchaseOrderEles.statusValue, expectedStatusAfterSaved);
  }

  /**
   * Clicks the "New" button to open Create Purchase Order page and wait page load completed.
   */
  async clickNew() {
    await click(purchaseOrderEles.buttonOnScreen(button().new));
    await waitForUrl(`${endpoint}/add`);
    await waitForPageLoadState({ waitUntil: 'networkidle' });
  }

  async clickSendToSupplier() {
    await click(purchaseOrderEles.button.sendToSupplier);
  }

  /**
   * Clicks the "Approve" button and waits for the approval dialog to disappear.
   */
  async approveSelectedFlow() {
    await click(purchaseOrderEles.buttonOnScreen(button().send));
    await purchaseOrderEles.sendToApproveDialog.title().waitFor({ state: 'hidden' });
    await waitForPageLoadState();
  }

  /**
   * Selects an approval flow from the dropdown and clicks the "Send" button.
   * @param flowName
   */
  async selectApproveFlowThenSend(flowName: string) {
    await fill(purchaseOrderEles.sendToApproveDialog.flowInput(), flowName);
    const option = purchaseOrderEles.option(flowName);
    await click(option);
  }

  /**
   * Opens the Purchase Order Details page via url.
   * @param purchaseOrderId - The ID of the purchase order to open.
   */
  async openPurchaseOrderDetails(purchaseOrderId: string): Promise<void> {
    await gotoURL(`${baseUrl}/${endpoint}/${purchaseOrderId}`, { waitUntil: 'networkidle' });
  }

  /**
   * Fills in the mandatory fields for creating or editing a purchase order.
   * @param supplierName - The name of the supplier to select.
   * @param department - The department to select.
   * @param internalNote - The internal note to enter.
   * @param currency - The currency to select (default is 'NOK').
   */
  async fillMandatoryFields(supplierName: string, department: string, internalNote: string, currency: string = 'NOK') {
    await sendKeys(purchaseOrderEles.supplier.input, supplierName, { delay: 100 });
    await click(purchaseOrderEles.option(supplierName, true));
    await waitForPageLoadState({ waitUntil: 'networkidle' });

    await sendKeys(purchaseOrderEles.department.input, department, { delay: 100 });
    await click(purchaseOrderEles.option(department, true));
    await waitForPageLoadState({ waitUntil: 'networkidle' });

    await fill(purchaseOrderEles.internalNoteInput, internalNote);
    const currentValue = await getText(purchaseOrderEles.currency.value(currency));
    if (currentValue.includes(currency)) {
      return;
    }
    await sendKeys(purchaseOrderEles.currency.input, currency, { delay: 100 });
    await click(purchaseOrderEles.option(currency, false));
  }

  /**
   * Selects a supplier contact from the dropdown.
   * @param contactName - The name of the contact to select.
   */
  async selectSupplierContact(contactName: string) {
    if (contactName === '') {
      const isVisible = await isElementVisible(purchaseOrderEles.reference.supplierContact.clearButton, {
        timeout: INSTANT_TIMEOUT,
      });
      if (isVisible) {
        await click(purchaseOrderEles.reference.supplierContact.clearButton);
        await releaseFocus(purchaseOrderEles.reference.supplierContact.input)
        await waitForPageLoadState({ waitUntil: 'networkidle' });
      }
      return;
    }
    await sendKeys(purchaseOrderEles.reference.supplierContact.input, contactName, { delay: 100 });
    await click(purchaseOrderEles.option(contactName, true));
    await waitForPageLoadState({ waitUntil: 'networkidle' });
  }

  /**
   * Verifies that the purchase order status matches the expected status.
   * @param expectedStatus - The expected status text to verify.
   */
  async verifyPurchaseOrderStatus(expectedStatus: string): Promise<void> {
    // Wait up to 30s for the status to change
    const timeout = MAX_TIMEOUT;
    const interval = STANDARD_TIMEOUT;
    const start = Date.now();

    while (Date.now() - start < timeout) {
      await reloadPage();
      await waitForPageLoadState();
      const currentStatus = await getInputValue(purchaseOrderEles.statusValue);
      if (currentStatus === expectedStatus) {
        break;
      }
      await new Promise(res => setTimeout(res, interval));
    }
    await expectElementToHaveValue(purchaseOrderEles.statusValue, expectedStatus);
  }

  /**
   * Verifies that the purchase order is created with the specified mandatory fields.
   * @param supplierName - The name of the supplier to verify.
   * @param department - The department to verify.
   * @param internalNote - The internal note to verify.
   * @param currency - The currency to verify (default is 'NOK').
   */
  async verifyPurchaseOrderCreatedWithMandatoryFields(
    supplierName: string,
    department: string,
    internalNote: string,
    currency: string = 'NOK',
  ) {
    await expectElementToHaveText(purchaseOrderEles.supplier.value(supplierName), supplierName);
    await expectElementToHaveText(purchaseOrderEles.department.value(department), department);
    await expectElementToHaveValue(purchaseOrderEles.internalNoteInput, internalNote);
    await expectElementToContainText(purchaseOrderEles.currency.value(currency), currency);
  }

  /**
   * Opens the "Add Line Item" dialog by clicking on the "Order Lines" tab.
   */
  async openAddLineItemDialog() {
    await click(purchaseOrderEles.tabs.orderLines);
    await click(purchaseOrderEles.button.add);
    await expectElementToBeVisible(addNewLineDialog.title(dialogTitle().addNewLine), 'visible');
  }

  /**
   * Adds a new line item to the purchase order.
   * @param sku - The SKU of the product to add.
   * @param lineText - The line text/description for the product.
   * @param quantity - The quantity of the product to add.
   * @param purchasePrice - The purchase price for the product.
   *
   */
  async addNewLine(sku: string, lineText: string, quantity: string | number, purchasePrice: string | number) {
    await sendKeys(addNewLineDialog.productSku.input, sku, { delay: 100 });
    await click(addNewLineDialog.option(sku, false));
    await releaseFocus(addNewLineDialog.productSku.input);
    await waitForPageLoadState({ waitUntil: 'networkidle' });
    await waitForSomeTime(1000);

    // change line text
    await fill(addNewLineDialog.lineText.input, lineText);
    await releaseFocus(addNewLineDialog.lineText.input);
    if ((await getInputValue(addNewLineDialog.lineText.input)) !== lineText) {
      await fill(addNewLineDialog.lineText.input, lineText);
      await releaseFocus(addNewLineDialog.lineText.input);
    }
    // change quantity
    await pressKey(addNewLineDialog.quantity.input, 'ControlOrMeta+A');
    await sendKeys(addNewLineDialog.quantity.input, `${quantity}`, { delay: 300 });
    await releaseFocus(addNewLineDialog.quantity.input);
    await waitForPageLoadState({ waitUntil: 'networkidle' });
    // change purchase price
    await pressKey(addNewLineDialog.purchasePrice.input, 'ControlOrMeta+A');
    await sendKeys(addNewLineDialog.purchasePrice.input, `${purchasePrice}`, { delay: 300 });
    await releaseFocus(addNewLineDialog.purchasePrice.input);
    await waitForPageLoadState({ waitUntil: 'networkidle' });

    // save the line item
    await click(addNewLineDialog.buttonOnScreen(button().save));
    await addNewLineDialog.title(dialogTitle().addNewLine).waitFor({ state: 'hidden' });
    await waitForPageLoadState();
  }

  /**
   * Verifies that a line item with the specified details exists in the order lines table.
   * @param sku - The SKU of the product to verify.
   * @param lineText - The line text/description to verify.
   * @param quantity - The quantity to verify.
   * @param purchasePrice - The purchase price to verify.
   */
  async verifyLineItemInTable(
    sku: string,
    lineText: string,
    quantity: string | number,
    purchasePrice: string | number,
  ) {
    const condition = { SKU: sku, 'Line Text': lineText };
    const values = { Quantity: `${quantity}`, 'Purchase Price': `${purchasePrice}` };
    await verifyRowData(getLocatorByRole('table'), condition, values);
  }

  /**
   * Verifies child items under a parent row in a table.
   * @param parentRow - The parent row to expand (identified by SKU and Line Text).
   * @param expectedValues - The expected values of the child items to verify.
   */
  async verifyChildItems(parentRow: { SKU: string; 'Line Text': string }, expectedValues: string[]) {
    const table = getLocatorByRole('table');
    const childTable = await expandRow(table, parentRow);
    await verifyRowData(childTable, 'SKU', expectedValues);
  }

  /**
   * Validates that the "Supplier contact is required" message is displayed in the Send to Supplier dialog.
   */
  async validateNoSupplier() {
    const content = langPack().isEnglish ? 'Supplier contact is required.' : 'Leverandørkontakt er nødvendig.';
    await expectElementToBeVisible(sendToSupplierDialog.content(content));
    await click(sendToSupplierDialog.close());
    await expectElementToBeHidden(sendToSupplierDialog.title(), { timeout: SMALL_TIMEOUT });
  }

  /**
   * Validates that the "Contact email is required" message is displayed in the Send to Supplier dialog.
   */
  async validateNoEmailInSupplier() {
    const content = langPack().isEnglish ? 'Contact email is required.' : 'Kontakt e-post er nødvendig.';
    await expectElementToBeVisible(sendToSupplierDialog.content(content));
    await click(sendToSupplierDialog.close());
    await expectElementToBeHidden(sendToSupplierDialog.title(), { timeout: SMALL_TIMEOUT });
  }
}
