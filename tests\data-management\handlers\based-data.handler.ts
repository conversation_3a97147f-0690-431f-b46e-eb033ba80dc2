import path from 'node:path';
import { loadJson } from '@common-helper';
import { BasedDataDocument } from '@test-data/models/based-data.model';

// Get BASED_DATA_PATH from environment or default
const BASED_DATA_PATH = process.env['BASED_DATA_PATH'] || path.resolve(process.cwd(), '.test-data/based_data.json');

/**
 * Loads all based-data documents from the test data file.
 */
function getAllBasedData(): BasedDataDocument[] {
  return loadJson<BasedDataDocument[]>(BASED_DATA_PATH);
}

/**
 * Gets the based-data for the current environment (env from NODE_ENV)
 */
export function getBasedData(env?: string): BasedDataDocument | undefined {
  const all = getAllBasedData();
  const targetEnv = env || process.env['NODE_ENV'] || 'dev';
  return all.find(doc => doc.env === targetEnv);
}
