/**
 * ui-action.ts: This module provides utility functions for retrieving text from web elements in web page and conditional statements with in Playwright.
 * These utilities include a variety of functions for retrieving text, input values, URLs, and checking conditions such as
 * whether an element is visible or checked. It provides a layer of abstraction over <PERSON><PERSON>'s built-in methods for
 * interacting with elements, making it easier to perform common tasks and checks on web elements.
 */

/**
 * 0. Common: This section contains functions and definition relate to all common function that would use with page object
 */
import { FrameLocator, Locator, selectors } from '@playwright/test';
import {
  GetByPlaceholderOptions,
  GetByRoleOptions,
  GetByRoleTypes,
  GetByTextOptions,
  LocatorOptions,
} from '@custom-types';
import { getPage } from '@page-utils';
import { handleException, checkNotEmpty } from '@report-helper';

/**
 * Waits for a given timeout in milliseconds.
 * This function uses the Playwright waitForTimeout method and handles potential errors.
 * @param timeout - The time to wait in milliseconds.
 * @returns {Promise<void>} - No return value, it just waits.
 */
export async function waitForSomeTime(timeout: number): Promise<void> {
  try {
    checkNotEmpty(timeout, 'Timeout'); // Check if the timeout is provided
    await getPage().waitForTimeout(timeout);
  } catch (error) {
    handleException(error, 'Error waiting for timeout');
  }
}

/**
 * Returns a Locator object based on the input provided.
 * This function handles invalid inputs (both string and Locator types).
 * @param input - The input to create the Locator from, either a string or an existing Locator.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocator(input: string | Locator, options?: LocatorOptions): Locator {
  try {
    checkNotEmpty(input, 'Input for Locator'); // Ensure the input is provided
    return typeof input === 'string' ? getPage().locator(input, options) : input;
  } catch (error) {
    handleException(error, 'Error getting Locator');
    // Ensure we throw the error to handle it upstream
  }
}

/**
 * Returns a Locator object with a specific testId.
 * Optionally accepts an attribute name to override the default 'testId' value.
 * @param testId - The testId to create the Locator from, either a string or RegExp.
 * @param attributeName - Optional attribute name for the testId.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByTestId(testId: string | RegExp, attributeName?: string): Locator {
  try {
    checkNotEmpty(testId, 'TestId'); // Ensure the testId is provided
    if (attributeName) {
      selectors.setTestIdAttribute(attributeName);
    }
    return getPage().getByTestId(testId);
  } catch (error) {
    handleException(error, 'Error getting Locator by TestId');
  }
}

/**
 * Returns a Locator object with a specific text.
 * @param text - The text to create the Locator from, either a string or RegExp.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByText(text: string | RegExp, options?: GetByTextOptions): Locator {
  try {
    checkNotEmpty(text, 'Text for Locator'); // Ensure the text is provided
    return getPage().getByText(text, options);
  } catch (error) {
    handleException(error, 'Error getting Locator by Text');
  }
}

/**
 * Returns a Locator object with a specific role.
 * @param role - The role to create the Locator from.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByRole(role: GetByRoleTypes, options?: GetByRoleOptions): Locator {
  try {
    checkNotEmpty(role, 'Role for Locator'); // Ensure the role is provided
    return getPage().getByRole(role, options);
  } catch (error) {
    handleException(error, 'Error getting Locator by Role');
  }
}

/**
 * Returns a Locator object with a specific label.
 * @param text - The label text to create the Locator from, either a string or RegExp.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByLabel(text: string | RegExp, options?: GetByRoleOptions): Locator {
  try {
    checkNotEmpty(text, 'Label for Locator'); // Ensure the label is provided
    return getPage().getByLabel(text, options);
  } catch (error) {
    handleException(error, 'Error getting Locator by Label');
  }
}

/**
 * Returns a Locator object with a specific title attribute.
 * @param text - The title text to create the Locator from, either a string or RegExp.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByTitle(text: string | RegExp, options?: GetByRoleOptions): Locator {
  try {
    checkNotEmpty(text, 'Title for Locator'); // Ensure the title is provided
    return getPage().getByTitle(text, options);
  } catch (error) {
    handleException(error, 'Error getting Locator by Title');
  }
}

/**
 * Returns a Locator object with a specific placeholder.
 * @param text - The placeholder text to create the Locator from, either a string or RegExp.
 * @param options - Optional parameters for the Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorByPlaceholder(text: string | RegExp, options?: GetByPlaceholderOptions): Locator {
  try {
    checkNotEmpty(text, 'Placeholder for Locator'); // Ensure the placeholder is provided
    return getPage().getByPlaceholder(text, options);
  } catch (error) {
    handleException(error, 'Error getting Locator by Placeholder');
  }
}

/**
 * Returns all Locator objects based on the input provided.
 * This function returns an array of Locator objects that match the input criteria.
 * @param input - The input to create the Locators from, either a string or an existing Locator.
 * @param options - Optional parameters for the Locators.
 * @returns {Promise<Locator[]>} - The created Locator objects.
 */
export async function getAllLocators(input: string | Locator, options?: LocatorOptions): Promise<Locator[]> {
  try {
    checkNotEmpty(input, 'Input for Locators'); // Ensure the input is provided
    return typeof input === 'string' ? await getPage().locator(input, options).all() : await input.all();
  } catch (error) {
    handleException(error, 'Error getting all Locators');
  }
}

/**
 * Returns a FrameLocator object based on the input provided.
 * @param frameInput - The input to create the FrameLocator from, either a string or an existing FrameLocator.
 * @returns {FrameLocator} - The created FrameLocator object.
 */
export function getFrameLocator(frameInput: string | FrameLocator): FrameLocator {
  try {
    checkNotEmpty(frameInput, 'Frame Input'); // Ensure the frame input is provided
    return typeof frameInput === 'string' ? getPage().frameLocator(frameInput) : frameInput;
  } catch (error) {
    handleException(error, 'Error getting FrameLocator');
  }
}

/**
 * Returns a Locator object within a specific frame based on the input provided.
 * @param frameInput - The input to create the FrameLocator from, either a string or an existing FrameLocator.
 * @param input - The input to create the Locator from, either a string or an existing Locator.
 * @returns {Locator} - The created Locator object.
 */
export function getLocatorInFrame(frameInput: string | FrameLocator, input: string | Locator): Locator {
  try {
    checkNotEmpty(frameInput, 'Frame Input'); // Ensure the frame input is provided
    checkNotEmpty(input, 'Locator Input'); // Ensure the locator input is provided
    return getFrameLocator(frameInput).locator(input);
  } catch (error) {
    handleException(error, 'Error getting Locator in Frame');
  }
}

/**
 * Converts a locator to a string representation suitable for logging.
 * This function handles the issue where Locator objects use Object's default stringification format.
 * @param input - The input to format, either a string or a Locator object.
 * @returns {string} - A string representation of the locator suitable for logging.
 */
export function getLocatorInfo(input: string | Locator): string {
  try {
    if (typeof input === 'string') {
      return input;
    }

    // Fallback to a generic description
    // eslint-disable-next-line @typescript-eslint/no-base-to-string
    return input.toString();
  } catch {
    // If anything goes wrong, return a fallback representation
    return typeof input === 'string' ? input : 'Locator[unable to format]';
  }
}
