import { CONTEXT_KEY, get, post } from '@api-helper';
import { handleException } from '@report-helper';
import { expect } from '@fixture-manager';
import { APIResponse } from '@playwright/test';
import { generateRandomString, getCurrentDateWithFormat } from '@common-helper';

const endPoint = 'logistics-api/logistics/purchase-orders';

/**
 * Create purchase order via API
 * @param supplierId - supplier id
 * @param departmentId - department id
 * @return {Promise<string>} - purchase order id
 */
export async function createPurchaseOrder(supplierId: string, departmentId: string): Promise<string> {
  try {
    // Create purchase order
    const createResponse = await post(`/${endPoint}`, {
      contextId: CONTEXT_KEY,
      body: {
        currencyId: 'NOK',
        deliveryDate: getCurrentDateWithFormat('yyyy-MM-dd'),
        deliveryMode: 1,
        departmentId: departmentId,
        discountRate: 0,
        internalNote: generateRandomString(10),
        supplierId: supplierId,
      },
    });
    expect(createResponse.ok()).toBeTruthy();
    const responseBody = await createResponse.text();
    return responseBody.replace('"', '');
  } catch (error) {
    handleException(error, 'Error while creating Purchase order via API');
  }
}

/**
 * Add order line to exist purchase order
 * @param purchaseId
 * @param skuId
 */
export async function addOrderLine(purchaseId: string, skuId: string) {
  try {
    // Create user
    const createResponse = await post(`/${endPoint}/${purchaseId}/lines`, {
      contextId: CONTEXT_KEY,
      body: {
        skuId: skuId,
        quantity: 1,
        purchasePrice: 100,
        pricePer: 90,
        purchasePriceInLocalCurrency: 0,
        linePriceRaw: 0,
        linePriceInLocalCurrency: 0,
        exchangeRate: 1,
        linePrice: 100,
        discountRate: 0,
        orderDiscountRate: 0,
        purchaseOrderId: purchaseId,
      },
    });
    expect(createResponse.ok()).toBeTruthy();
  } catch (error) {
    handleException(error, 'Error while adding order line via API');
  }
}

/**
 * Get purchase order by status
 * @param statusId - status id draft = 8
 * @return {Promise<APIResponse>} - API response
 */
export async function getPurchaseOrder(statusId: string): Promise<APIResponse> {
  try {
    const response = await get(`/${endPoint}`, {
      contextId: CONTEXT_KEY,
      queryParams: {
        pageNumber: 1,
        pageSize: 10,
        filter: `statusId="${statusId}"`,
        isTakeAll: false,
      },
    });
    expect(response.ok()).toBeTruthy();
    return response;
  } catch (error) {
    handleException(error, 'Error while retrieving Purchase order');
  }
}

/**
 * Check if purchase order includes lines
 * @param purchaseId - purchase order id
 * @return {Promise<boolean>} - true if purchase order includes lines, false otherwise
 */
export async function isIncludeLines(purchaseId: string): Promise<boolean> {
  try {
    const response = await get(`/${endPoint}/${purchaseId}`, { contextId: CONTEXT_KEY });
    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    return responseBody?.lines?.length > 0;
  } catch (error) {
    handleException(error, 'Error while retrieving Purchase order details');
  }
}
