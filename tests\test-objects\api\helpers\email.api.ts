import { CONTEXT_COOKIE, get } from '@api-helper';
import { handleException } from '@report-helper';
import { expect } from '@fixture-manager';
import { APIResponse } from '@playwright/test';

const endPoint = 'email';
export type emailFilter = {
  toEmail?: string | undefined;
  fromEmail?: string | undefined;
  timeStamp?: string | undefined;
  templateName?: string | undefined;
  subject?: string | undefined;
};

/**
 * Get email from history with filter
 * @param fromDate - MM/dd/yyyy
 * @param toDate - MM/dd/yyyy
 * @param filter - filter object { toEmail?: string; fromEmail?: string; timeStamp?: '09/22/2025 10:18 AM'; templateName?: string; subject?: string }
 * @returns Email object or undefined
 * @throws Error if there is an issue with the API call
 * @example
 *        return Email: {
 *            "Id": "50d3553a-3f7f-41e8-8060-b9184fa981cf",
 *            "To": " [thuan.pham@upheads.no111]",
 *            "From": "<EMAIL>",
 *            "CcEmail": "",
 *            "Subject": "Leverandørfaktura er godkjent",
 *            "Timestamp": "09/22/2025 07:00 AM",
 *            "Priority": 50,
 *            "TemplateName": "3 - Supplier Invoice approved"
 *        }
 */
export async function getEmailFromHistory(fromDate: string, toDate: string, filter: emailFilter): Promise<any> {
  try {
    const response = await get(`/${endPoint}/GetEmailHistories`, {
      contextId: CONTEXT_COOKIE,
      queryParams: {
        fromDate: fromDate,
        toDate: toDate,
        sord: 'asc',
        page: 1,
        rows: 1000,
      },
    });
    return await handleEmailResponse(response, filter);
  } catch (error) {
    handleException(error, 'Error while retrieving get approve flows');
  }
}

/**
 * Get email from queue with filter
 * @param fromDate - MM/dd/yyyy
 * @param toDate - MM/dd/yyyy
 * @param filter - filter object { toEmail?: string; fromEmail?: string; timeStamp?: '09/22/2025 10:18 AM'; templateName?: string; subject?: string }
 * @returns Email object or undefined
 * @throws Error if there is an issue with the API call
 * @example
 *        return Email: {
 *            "Id": "50d3553a-3f7f-41e8-8060-b9184fa981cf",
 *            "To": " [thuan.pham@upheads.no111]",
 *            "From": "<EMAIL>",
 *            "CcEmail": "",
 *            "Subject": "Leverandørfaktura er godkjent",
 *            "Timestamp": "09/22/2025 07:00 AM",
 *            "Priority": 50,
 *            "TemplateName": "3 - Supplier Invoice approved"
 *        }
 */
export async function getEmailFromQueue(fromDate: string, toDate: string, filter: emailFilter): Promise<any> {
  try {
    const response = await get(`/${endPoint}/GetQueueEmails`, {
      contextId: CONTEXT_COOKIE,
      queryParams: {
        fromDate: fromDate,
        toDate: toDate,
        sord: 'asc',
        status: '',
        page: 1,
        rows: 1000,
      },
    });
    return await handleEmailResponse(response, filter);
  } catch (error) {
    handleException(error, 'Error while retrieving get approve flows');
  }
}

async function handleEmailResponse(response: APIResponse, filter: emailFilter): Promise<any> {
  expect(response.ok()).toBeTruthy();
  const data = await response.json();
  let items: any[] = data.rows || [];

  // normalize email fields (trim and lowercase)
  const normalize = (val?: unknown): string =>
    typeof val === 'string'
      ? val
          .trim()
          .replace(/^\[|]$/g, '')
          .toLowerCase()
      : '';

  if (filter.toEmail) {
    const target = normalize(filter.toEmail);
    items = items.filter(item => normalize(item.To) === target);
  }

  if (filter.fromEmail) {
    const target = normalize(filter.fromEmail);
    items = items.filter(item => normalize(item.From) === target);
  }

  if (filter.timeStamp) {
    const ts = filter.timeStamp.trim();
    items = items.filter(item => normalize(item.Timestamp).startsWith(ts));
  }

  if (filter.templateName) {
    const target = filter.templateName.trim().toLowerCase();
    items = items.filter(item => normalize(item.TemplateName).includes(target));
  }

  if (filter.subject) {
    const target = filter.subject.trim().toLowerCase();
    items = items.filter(item => normalize(item.Subject).includes(target));
  }

  return items.length ? items[0] : undefined;
}
