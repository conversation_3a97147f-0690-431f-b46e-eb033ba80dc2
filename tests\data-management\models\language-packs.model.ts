// data-management/models/language-packs.model.ts

export interface LanguageRoot {
  en: LanguagePack;
  no: LanguagePack;
}

export interface LanguagePack {
  isEnglish: boolean;
  language: string;

  general: General;
  purchaseOrSalesOrder: PurchaseOrSalesOrder;
  buttons: Button;
  deal: Deal;
  contact: Contact;
  dialogTitle: DialogTitle;
  status: Status;
  tabName: TabName;
  hour: Hour;
  customerWeb: CustomerWeb;
}

// ✅ General strings (shared top-level)
export interface General {
  department: string;
  category: string;
  title: string;
  company: string;
  project: string;
  projectName: string;
  invoice: string;
  timestamp: string;
  internalComment: string;
  emailInformation: string;
  name: string;
  description: string;
  supplier: string;
  supplierName: string;
  article: string;
  articleNo: string;
  customer: string;
  createdDate: string;
  type: string;
  sku: string;
  quantity: string;
  date: string;
  year: string;
  rate: string;
  distance: string;
  amount: string;
  additionalRate: string;
  price: string;
}

// ✅ Buttons
export interface Button {
  cancel: string;
  ok: string;
  arrowLeft: string;
  history: string;
  addFromTemplate: string;
  saveAndClose: string;
  createPo: string;
  addDeal: string;
  splitRegistration: string;
  reSendStatus: string;
  override: string;
  hourOverview: string;
  createDelivery: string;
  createInvoice: string;
  search: string;
  sendToApprove: string;
  send: string;
  sendToSupplier: string;
  addNew: string;
  newProject: string;
  new: string;
  addNewTickets: string;
  newEquipment: string;
  generate: string;
  save: string;
  add: string;
  approve: string;
  close: string;
  moveHours: string;
  finish: string;
  runInvoicing: string;
  actions: string;
  newRegistration: string;
  move: string;
}

// ✅ Customer Web
export interface CustomerWeb {
  contacts: string;
  usersWithNoLicense: string;
  companyInfo: string;
  equipment: string;
}

// ✅ Status
export interface Status {
  delivered: string;
  success: string;
  draft: string;
  invoiced: string;
  confirmed: string;
  active: string;
  inactive: string;
  approved: string;
  readyForApproving: string;
}

// ✅ Tabs
export interface TabName {
  orderLines: string;
  invoices: string;
  workOrders: string;
  delivery: string;
  activities: string;
  sales: string;
  personal: string;
}

// ✅ Dialog Titles
export interface DialogTitle {
  addNewLine: string;
  agreementConfirmation: string;
  registerHours: string;
  workOrder: string;
  createCustomerInvoice: string;
  delivery: string;
  editContact: string;
  quickCreateContact: string;
}

// ✅ Purchase/Sales Order
export interface PurchaseOrSalesOrder {
  orderNo: string;
  salePriceAfterDiscount: string;
  vatCode: string;
  productSku: string;
  purchaseOrder: string;
  quantityToDeliver: string;
  currency: string;
  orderStatus: string;
  internalNote: string;
  deliveryDate: string;
  salesPrice: string;
  purchasePrice: string;
  lineText: string;
  warehouse: string;
  supplierName: string;
  registrationDate: string;
  priceNoK: string;
  batchNo: string;
  costPrice: string;
  premiums: string;
  discount: string;
  linePriceNok: string;
  linePrice: string;
  deliveryNo: string;
}

// ✅ Deal
export interface Deal {
  selectServiceToReplace: string;
  leadFrom: string;
  responsiblePerson: string;
  descriptionIsRequired: string;
  signedAgreement: string;
  regenerateFile: string;
  showActivities: string;
  commentForCustomer: string;
  reviewYourDetailAndSubmit: string;
  agreementDate: string;
  invoiceStartDate: string;
  agreementNo: string;
  dealStage: string;
  emailWithCustomer: string;
}

// ✅ Hours
export interface Hour {
  transportType: string;
  approvedDate: string;
  estimatedHours: string;
  person: string;
  activity: string;
  registerHours: string;
  billableHours: string;
  invoiced: string;
  additionalExpNo: string;
  drivingFee: string;
  fromDate: string;
  hoursWorked: string;
  travelExpenses: string;
  additionalExpenses: string;
  additionalAmount: string;
  miscHardware: string;
  miscHardwareAmount: string;
}

// ✅ Contact
export interface Contact {
  contact: string;
  title: string;
  email: string;
  firstName: string;
  lastName: string;
  inactive: string;
  notes: string;
}
