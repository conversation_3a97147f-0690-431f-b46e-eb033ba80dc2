const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');
const { Command } = require('commander');
require('dotenv').config({ path: path.resolve(process.cwd(), 'environments/db.env') });

class MongoDbClient {
  constructor() {
    this.client = null;
    this.db = null;
    this.config = {
      connectionString: process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017',
      databaseName: process.env.MONGODB_DATABASE_NAME || 'test_automation',
      timeout: parseInt(process.env.MONGODB_TIMEOUT || '30000'),
      maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '10'),
      minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE || '2'),
    };
  }

  async connect() {
    try {
      this.client = new MongoClient(this.config.connectionString, {
        serverSelectionTimeoutMS: this.config.timeout,
        maxPoolSize: this.config.maxPoolSize,
        minPoolSize: this.config.minPoolSize,
      });

      await this.client.connect();
      this.db = this.client.db(this.config.databaseName);
      console.log(`Connected to MongoDB database: ${this.config.databaseName}`);
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      console.log('Disconnected from MongoDB');
    }
  }

  getCollection(collectionName) {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db.collection(collectionName);
  }

  async exportCollectionToJson(options) {
    try {
      if (!this.db) {
        throw new Error('Database not connected. Call connect() first.');
      }

      const collection = this.getCollection(options.collectionName);

      const queryOptions = {};
      if (options.projection) {
        queryOptions.projection = options.projection;
      }
      if (options.limit) {
        queryOptions.limit = options.limit;
      }

      console.log(`Fetching data from collection: ${options.collectionName}`);
      const cursor = collection.find(options.query || {}, queryOptions);
      const data = await cursor.toArray();

      console.log(`Found ${data.length} documents`);

      const outputDir = path.dirname(options.outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`Created directory: ${outputDir}`);
      }

      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(options.outputPath, jsonData, 'utf8');

      console.log(`Data exported successfully to: ${options.outputPath}`);
      console.log(`File size: ${Math.round(jsonData.length / 1024)} KB`);
    } catch (error) {
      console.error('Failed to export collection data:', error);
      throw error;
    }
  }

  async testConnection() {
    try {
      await this.connect();
      await this.db.admin().ping();
      console.log('Database connection test successful');
      await this.disconnect();
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  async listCollections() {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }

    try {
      const collections = await this.db.listCollections().toArray();
      return collections.map(col => col.name);
    } catch (error) {
      console.error('Failed to list collections:', error);
      throw error;
    }
  }

  async getCollectionCount(collectionName, query = {}) {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }

    try {
      const collection = this.getCollection(collectionName);
      return await collection.countDocuments(query);
    } catch (error) {
      console.error(`Failed to get count for collection ${collectionName}:`, error);
      throw error;
    }
  }
}

const program = new Command();

program.name('init-testdata').description('Initialize test data from MongoDB collections').version('1.0.0');

program
  .option('-c, --collection <name>', 'Collection name to export')
  .option('-o, --output <path>', 'Output file path for JSON data')
  .option('-q, --query <json>', 'MongoDB query filter as JSON string')
  .option('-p, --projection <json>', 'Field projection as JSON string')
  .option('-l, --limit <number>', 'Maximum number of documents to export')
  .option('--list', 'List all available collections')
  .option('--test', 'Test database connection')
  .action(async options => {
    const dbClient = new MongoDbClient();

    try {
      if (options.test) {
        console.log('Testing database connection...');
        const isConnected = await dbClient.testConnection();
        if (isConnected) {
          console.log('✅ Database connection successful');
          process.exit(0);
        } else {
          console.log('❌ Database connection failed');
          process.exit(1);
        }
      }

      await dbClient.connect();

      if (options.list) {
        console.log('📋 Available collections:');
        const collections = await dbClient.listCollections();
        collections.forEach((collection, index) => {
          console.log(`  ${index + 1}. ${collection}`);
        });
        await dbClient.disconnect();
        return;
      }

      if (!options.collection || !options.output) {
        console.error('❌ Error: --collection and --output are required for data export');
        console.log('\nUsage examples:');
        console.log('  npm run init-testdata -- --collection users --output ./tests/test-data/users.json');
        console.log('  npm run init-testdata -- --list');
        console.log('  npm run init-testdata -- --test');
        await dbClient.disconnect();
        process.exit(1);
      }

      let query;
      let projection;
      let limit;

      if (options.query) {
        try {
          query = JSON.parse(options.query);
        } catch {
          console.error('❌ Error: Invalid JSON in --query parameter');
          await dbClient.disconnect();
          process.exit(1);
        }
      }

      if (options.projection) {
        try {
          projection = JSON.parse(options.projection);
        } catch {
          console.error('❌ Error: Invalid JSON in --projection parameter');
          await dbClient.disconnect();
          process.exit(1);
        }
      }

      if (options.limit) {
        limit = parseInt(options.limit);
        if (isNaN(limit) || limit <= 0) {
          console.error('❌ Error: --limit must be a positive number');
          await dbClient.disconnect();
          process.exit(1);
        }
      }

      const totalCount = await dbClient.getCollectionCount(options.collection, query || {});
      console.log(`📊 Total documents in collection '${options.collection}': ${totalCount}`);

      if (totalCount === 0) {
        console.log('⚠️  No documents found matching the query criteria');
        await dbClient.disconnect();
        return;
      }

      const outputPath = path.resolve(options.output);

      console.log(`🚀 Starting data export...`);
      await dbClient.exportCollectionToJson({
        collectionName: options.collection,
        outputPath: outputPath,
        query: query,
        projection: projection,
        limit: limit,
      });

      console.log('✅ Data export completed successfully!');

      if (limit && limit < totalCount) {
        console.log(`📝 Exported ${limit} out of ${totalCount} documents (limited)`);
      } else {
        console.log(`📝 Exported all ${totalCount} documents`);
      }
    } catch (error) {
      console.error('❌ Error during data initialization:', error);
      process.exit(1);
    } finally {
      await dbClient.disconnect();
    }
  });

program.parse();
