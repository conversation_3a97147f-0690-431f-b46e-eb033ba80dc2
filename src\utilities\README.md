# src/utilities/

Purpose: Reusable utilities for UI, API, reporting, and common helpers.

Structure:

- ui/: page-utils (lifecycle), page-actions (wrappers), parameter-types
- api/: api-helper (context + request), api-config, types
- reporter/: custom-logger, custom-expect, monocart-config
- common/: data-loader and shared helpers

Extending:

- Add new utilities under the appropriate folder and export them via path aliases
- Include JSDoc with examples and integrate logging using custom-logger

### Example: Adding a New Utility

1. Create a new file under the appropriate folder (e.g., `src/utilities/common/new-helper.ts`).
2. Implement the utility function:

```typescript
// new-helper.ts
export function newUtilityFunction(param: string): string {
  return `Processed: ${param}`;
}
```

3. Export the utility in the folder's `index.ts`:

```typescript
export * from './new-helper';
```

4. Use the utility in your code:

```typescript
import { newUtilityFunction } from '@common';
console.log(newUtilityFunction('example'));
```

Guidelines:

- No relative imports from tests; use aliases like @page-action, @api/_, @reporter/_
- Mask sensitive data in logs
- Validate inputs and throw meaningful errors

Relationships:

- UI utilities depend on Playwright Page lifecycle via @page-utils
- API utilities depend on Playwright APIRequestContext
