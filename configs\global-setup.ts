import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { config as dotenvConfig } from 'dotenv';
import { logger } from '@report-helper';

/**
 * Global setup loads environment variables and prepares shared resources.
 */
export default function globalSetup(): void {
  const env = process.env['NODE_ENV'] || 'dev';
  const __dirname = path.dirname(fileURLToPath(import.meta.url));
  const envPath = path.resolve(__dirname, `../environments/${env}.env`);
  dotenvConfig({ path: envPath });
  logger.info(`Global setup complete. Loaded env: ${env} from ${envPath}`);
}
