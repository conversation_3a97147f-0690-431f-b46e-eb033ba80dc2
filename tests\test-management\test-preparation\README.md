# Test Preparation Module

This module contains utilities for preparing test environments and managing test data, including user account management for authentication scenarios with multi-environment support.

## AccountManager Class

The `AccountManager` class provides a comprehensive solution for managing test accounts across multiple environments (dev, staging, release) with different roles. It loads account configurations from `tests/test-data/users.json` and supports environment-specific password management.

### Supported Environments

- **`dev`** - Development environment
- **`staging`** - Staging/Testing environment
- **`release`** - Production/Release environment

### Supported Roles

- **`Admin`** - Administrator user role for Khaco-specific functionality
- **`Other`** - Alternative user role for edge cases and special scenarios
- **`Normal`** - Standard user role for general testing (default)

### Environment Variables

Set these environment variables to customize account passwords for each environment:

**Development Environment:**

- `DEV_ADMIN_PASSWORD` - Password for Admin role in dev
- `DEV_OTHER_PASSWORD` - Password for Other role in dev
- `DEV_NORMAL_PASSWORD` - Password for Normal role in dev

**Staging Environment:**

- `STAGING_ADMIN_PASSWORD` - Password for Admin role in staging
- `STAGING_OTHER_PASSWORD` - Password for Other role in staging
- `STAGING_NORMAL_PASSWORD` - Password for Normal role in staging

**Release Environment:**

- `RELEASE_ADMIN_PASSWORD` - Password for Admin role in release
- `RELEASE_OTHER_PASSWORD` - Password for Other role in release
- `RELEASE_NORMAL_PASSWORD` - Password for Normal role in release

If not set, default passwords from the JSON file will be used.

### Configuration File

Accounts are defined in `tests/test-data/users.json` with the following structure:

```json
{
  "dev": {
    "Admin": [
      {
        "displayName": "Dev Khaco Admin User",
        "username": "khaco_admin_dev",
        "email": "<EMAIL>",
        "passwordEnvKey": "DEV_ADMIN_PASSWORD",
        "defaultPassword": "DevAdminSecret123!",
        "role": "Admin",
        "id": "admin_dev_1"
      }
    ],
    "Other": [...],
    "Normal": [...]
  },
  "staging": {...},
  "release": {...}
}
```

### Basic Usage

#### Environment Management

```typescript
import { AccountManager, setEnvironment, getCurrentEnvironment } from './account-manager';

// Set environment (defaults to NODE_ENV or 'dev')
setEnvironment('staging');

// Get current environment
const currentEnv = getCurrentEnvironment(); // 'staging'

// Using class directly
const accountManager = AccountManager.getInstance();
accountManager.setEnvironment('release');
```

#### Account Acquisition

```typescript
import { acquireAccount } from './account-manager';

// Get account from current environment
const account = await acquireAccount('Admin');

// Get account from specific environment
const stagingAccount = await acquireAccount('Normal', undefined, 'staging');

// Get specific account by ID
const specificAccount = await acquireAccount('Admin', 'admin_dev_1', 'dev');

// Account structure includes environment-specific data
console.log(account.displayName); // "Staging Khaco Admin User"
console.log(account.username); // "khaco_admin_staging"
console.log(account.email); // "<EMAIL>"
console.log(account.role); // "Admin"
```

#### Using AccountManager Class

```typescript
import { AccountManager } from './account-manager';

const accountManager = AccountManager.getInstance();

// Set environment
accountManager.setEnvironment('staging');

// Acquire accounts
const adminAccount = await accountManager.acquireAccount('Admin');
const devNormalAccount = await accountManager.acquireAccount('Normal', undefined, 'dev');

// Get all accounts for a role in specific environment
const allDevAdmins = accountManager.getAccountsByRole('Admin', 'dev');

// Add account to specific environment
accountManager.addAccount(
  {
    displayName: 'Custom Test Admin',
    username: 'custom_admin',
    email: '<EMAIL>',
    password: 'CustomPassword123!',
    role: 'Admin',
    id: 'custom_admin_1',
  },
  'dev',
);

// Get statistics for all environments
const allStats = accountManager.getAccountStats();
console.log(allStats);
// Output: {
//   dev: { Admin: { total: 2 }, Other: { total: 1 }, Normal: { total: 1 } },
//   staging: { Admin: { total: 1 }, Other: { total: 1 }, Normal: { total: 1 } }
// }

// Get statistics for specific environment
const devStats = accountManager.getAccountStats('dev');

// Reload accounts from file (useful for runtime updates)
accountManager.reloadAccounts();
```

### Integration with Playwright Fixtures

#### Environment-Aware Auth Setup

```typescript
import { test as baseTest } from '@playwright/test';
import { acquireAccount, setEnvironment } from '../test-preparation/account-manager';

export const test = baseTest.extend<{}, { workerStorageState: string }>({
  workerStorageState: [
    async ({ browser }, use) => {
      // Set environment based on test configuration
      const testEnv = process.env.TEST_ENV || 'dev';
      setEnvironment(testEnv as any);

      const account = await acquireAccount('Normal');

      const page = await browser.newPage();

      // Perform authentication with environment-specific account
      await page.goto(`https://${testEnv}.example.com/login`);
      await page.fill('[name="username"]', account.username);
      await page.fill('[name="password"]', account.password);
      await page.click('[type="submit"]');

      // Save auth state
      const fileName = `auth-${testEnv}-${test.info().parallelIndex}.json`;
      await page.context().storageState({ path: fileName });

      await page.close();
      await use(fileName);
    },
    { scope: 'worker' },
  ],
});
```

#### Multi-Environment Test Example

```typescript
import { test } from './auth.setup';
import { acquireAccount, setEnvironment } from '../test-preparation/account-manager';

test.describe('Multi-Environment Testing', () => {
  test('Dev environment admin functionality', async ({ page }) => {
    setEnvironment('dev');
    const adminAccount = await acquireAccount('Admin');

    console.log(`Testing in DEV with: ${adminAccount.displayName}`);
    await page.goto('https://dev.example.com/admin');
    // ... test steps
  });

  test('Staging environment user flow', async ({ page }) => {
    const stagingAccount = await acquireAccount('Normal', undefined, 'staging');

    console.log(`Testing in STAGING with: ${stagingAccount.displayName}`);
    await page.goto('https://staging.example.com/dashboard');
    // ... test steps
  });

  test('Cross-environment account comparison', async ({ page }) => {
    const devAccount = await acquireAccount('Admin', undefined, 'dev');
    const stagingAccount = await acquireAccount('Admin', undefined, 'staging');

    // Compare behavior between environments
    console.log(`Dev Admin: ${devAccount.username}`);
    console.log(`Staging Admin: ${stagingAccount.username}`);
  });
});
```

### Configuration Management

#### Adding New Environments

To add a new environment, simply add it to the `users.json` file:

```json
{
  "dev": {...},
  "staging": {...},
  "release": {...},
  "production": {
    "Admin": [
      {
        "displayName": "Production Admin User",
        "username": "admin_prod",
        "email": "<EMAIL>",
        "passwordEnvKey": "PROD_ADMIN_PASSWORD",
        "defaultPassword": "ProdAdminSecret123!",
        "role": "Admin",
        "id": "admin_prod_1"
      }
    ]
  }
}
```

#### Environment-Specific Password Setup

```bash
# .env.dev
DEV_ADMIN_PASSWORD=DevAdminSecret123!
DEV_OTHER_PASSWORD=DevOtherSecret123!
DEV_NORMAL_PASSWORD=DevNormalSecret123!

# .env.staging
STAGING_ADMIN_PASSWORD=StagingAdminSecret123!
STAGING_OTHER_PASSWORD=StagingOtherSecret123!
STAGING_NORMAL_PASSWORD=StagingNormalSecret123!

# .env.release
RELEASE_ADMIN_PASSWORD=ReleaseAdminSecret123!
RELEASE_OTHER_PASSWORD=ReleaseOtherSecret123!
RELEASE_NORMAL_PASSWORD=ReleaseNormalSecret123!
```

### Best Practices

1. **Environment Isolation**: Use different account credentials for each environment
2. **Secure Passwords**: Always use environment variables for production passwords
3. **Clear Naming**: Use descriptive display names that include environment context
4. **Environment Detection**: Automatically set environment based on NODE_ENV when possible
5. **Fallback Strategy**: The system provides fallback accounts if file loading fails

### Error Handling

The AccountManager includes comprehensive error handling:

- **File Not Found**: Falls back to hardcoded accounts with logging
- **Invalid Environment**: Throws clear error messages
- **Missing Accounts**: Detailed error messages including environment and role context
- **JSON Parse Errors**: Graceful fallback with error logging

### API Reference

#### Environment Management

- `setEnvironment(environment)` - Set current environment
- `getCurrentEnvironment()` - Get current environment
- `getAvailableEnvironments()` - Get all available environments

#### Account Management

- `acquireAccount(role?, id?, environment?)` - Get account with optional environment override
- `getAccountsByRole(role, environment?)` - Get all accounts for role in environment
- `addAccount(account, environment?)` - Add account to specific environment
- `getAccountById(id, environment?)` - Find account by ID in environment

#### Statistics and Utilities

- `getAccountStats(environment?)` - Get statistics for all or specific environment
- `reloadAccounts()` - Reload accounts from file

### Migration Guide

#### From Previous Version

```typescript
// Old way (single environment)
const account = await acquireAccount('Normal');

// New way (environment-aware)
setEnvironment('staging');
const account = await acquireAccount('Normal');

// Or specify environment directly
const account = await acquireAccount('Normal', undefined, 'staging');
```

#### Environment Configuration

1. Update your `users.json` file with the new multi-environment structure
2. Set appropriate environment variables for each environment
3. Update your test configuration to set the environment based on test context
4. Use environment-specific URLs and configurations in your tests

The system automatically detects the environment from `NODE_ENV` or defaults to 'dev', making migration seamless for existing tests.
