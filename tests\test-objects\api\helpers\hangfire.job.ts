import { CONTEXT_KEY, post } from '@api-helper';
import { expect } from '@fixture-manager';
import { handleException } from '@report-helper';

const endPoint = '/Jobs/internalCommands/triggerJob/';

/**
 * Trigger email job from hangfire
 */
export async function triggerEmailJob() {
  try {
    const response = await post(`${endPoint}/EmailJob`, {
      contextId: CONTEXT_KEY,
    });
    expect(response.ok()).toBeTruthy();
  } catch (ex) {
    handleException(ex, 'Error while triggering email job');
  }
}
