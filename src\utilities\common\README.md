# Common Utilities

Generic helpers shared across UI, API, DB, and tests.

## Files

- `datetime-utils.ts`: Date/time formatting and parsing helpers.
- `number-utils.ts`: Numeric formatting, rounding, random generators.
- `object-utils.ts`: Deep merge, clone, diff utilities.
- `string-utils.ts`: String casing, truncation, slug helpers.
- `data-loader.ts`: Loads JSON/YAML/static data assets with caching.

## Guidelines

- Keep pure & side-effect free (except `data-loader`).
- Provide JSDoc and unit-level examples inside comments.
- Avoid introducing dependencies—prefer native implementations.

### Examples

#### Example: Using `datetime-utils`

```typescript
import { formatDate } from '@common/datetime-utils';
console.log(formatDate(new Date(), 'YYYY-MM-DD'));
```

#### Example: Using `number-utils`

```typescript
import { roundTo } from '@common/number-utils';
console.log(roundTo(3.14159, 2)); // Outputs: 3.14
```

#### Example: Using `object-utils`

```typescript
import { deepMerge } from '@common/object-utils';
const obj1 = { a: 1 };
const obj2 = { b: 2 };
console.log(deepMerge(obj1, obj2)); // Outputs: { a: 1, b: 2 }
```

#### Example: Using `string-utils`

```typescript
import { toSlug } from '@common/string-utils';
console.log(toSlug('Hello World!')); // Outputs: hello-world
```
