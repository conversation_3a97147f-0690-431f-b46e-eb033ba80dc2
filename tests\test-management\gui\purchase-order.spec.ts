import { test } from '@fixture-manager';
import { expectElementToBeVisible } from '@ui-action';
import {
  addOrderLine,
  createPurchaseOrder,
  getPurchaseOrder,
  isIncludeLines,
} from '@api-objects/helpers/purchase-order.api';
import { getBasedData } from '@test-data/handlers/based-data.handler';
import { langPack, status } from '@test-data/handlers/language-packs.handler';
import {
  addApproveStep,
  createApproveFlow,
  getApproveFlow,
  getStepByFlowId,
} from '@api-objects/helpers/approve-flow.api';
import { MenuPaths, navigateToMenu } from '@components/navigation.func';
import { generateRandomString, getCurrentDateWithFormat, randomNumberString } from '@common-helper';
import PurchaseOrderPage from '@page-objects/purchase-order.page';
import { raiseError } from '@report-helper';
import { createContact, getContract } from '@api-objects/helpers/company.api';
import { expectToastMessageContains } from '@components/toast.func';
import { triggerEmailJob } from '@api-objects/helpers/hangfire.job';
import { getEmailFromHistory } from '@api-objects/helpers/email.api';

test.describe('Purchase Order', () => {
  const data = getBasedData()?.data;
  const mandatoryFields = {
    supplier: data?.supplier[0] || '',
    department: data?.department[0]?.name || '',
    internalNote: generateRandomString(50),
  };
  let lineItem: {
    sku: string;
    lineText: string;
    quantity: string;
    purchasePrice: string;
  };

  /**
   * Open a random Draft purchase order which has lines
   * @param purchaseOrder - PurchaseOrderPage instance
   * @param addLineItem - Whether to add a line item if the PO has no lines (default: true)
   */
  async function openRandomDraftPurchaseOrderId(purchaseOrder: PurchaseOrderPage, addLineItem: boolean = true) {
    await test.step('Find and open a Draft purchase order which has lines', async () => {
      const response = await getPurchaseOrder('8');
      const purchaseOrders: any = await response?.json();
      const items: any[] = purchaseOrders.items;
      let id: string;
      if (items.length === 0) {
        // create new PO
        id = await createPurchaseOrder(data?.supplier[0] || '', data?.department[0]?.id || '');
      } else {
        const randomIndex = Math.floor(Math.random() * items.length);
        const po = items[randomIndex];
        id = po.id;
      }
      if (addLineItem) {
        const isExistLines: boolean = await isIncludeLines(id);
        if (!isExistLines) {
          const skuId = data?.product.find(p => p.isStockItem)?.id || '';
          // Add a comment to manually add a line item
          await addOrderLine(id, skuId);
        }
      }

      await purchaseOrder.openPurchaseOrderDetails(id);
      // Test implementation
      await expectElementToBeVisible('//img[@title="Bravo 365" or @alt="Logo"]');
    });
  }

  /**
   * Add a line item to a random Draft purchase order
   * @param sku - SKU of the product to add
   * @param purchaseOrder - PurchaseOrderPage instance
   */
  async function addLineItemToPurchaseOrder(sku: string, purchaseOrder: PurchaseOrderPage) {
    lineItem = {
      sku,
      lineText: generateRandomString(15),
      quantity: randomNumberString(1, 10),
      purchasePrice: randomNumberString(10, 20),
    };

    await openRandomDraftPurchaseOrderId(purchaseOrder, false);

    await test.step('Add a line item to the Purchase Order', async () => {
      await purchaseOrder.openAddLineItemDialog();
      await purchaseOrder.addNewLine(lineItem.sku, lineItem.lineText, lineItem.quantity, lineItem.purchasePrice);
    });

    await test.step('Verify the line item is added successfully', async () => {
      await purchaseOrder.verifyLineItemInTable(
        lineItem.sku,
        lineItem.lineText,
        lineItem.quantity,
        lineItem.purchasePrice,
      );
    });
  }

  test.beforeEach(async () => {
    await test.step('Create a new Purchase Order', async () => {
      await navigateToMenu(MenuPaths.CRM['Purchase/Sales Order'].PurchaseOrders);
    });
  });

  test('Create Purchase Order', async ({ purchaseOrder }) => {
    await test.step('Create a new Purchase Order', async () => {
      await purchaseOrder.clickNew();
    });

    await test.step('Filling mandatory fields and save the Purchase Order', async () => {
      await purchaseOrder.fillMandatoryFields(
        mandatoryFields.supplier,
        mandatoryFields.department,
        mandatoryFields.internalNote,
      );
      await purchaseOrder.clickSave(status().draft);
    });

    await test.step('Verify the Purchase Order is created successfully', async () => {
      await purchaseOrder.verifyPurchaseOrderStatus(status().draft);
      await purchaseOrder.verifyPurchaseOrderCreatedWithMandatoryFields(
        mandatoryFields.supplier,
        mandatoryFields.department,
        mandatoryFields.internalNote,
      );
    });
  });

  test('Add line item to Purchase Order', async ({ purchaseOrder }) => {
    const sku = data?.product.find(p => p.isStockItem)?.skuNumber || '';
    await addLineItemToPurchaseOrder(sku, purchaseOrder);
  });

  test('Add bundle to line item to Purchase Order', async ({ purchaseOrder }) => {
    const product = data?.product.find(p => p.bundleItems.length > 0) || null;
    if (!product) {
      raiseError('No bundle product found in prepare test data');
    }
    await addLineItemToPurchaseOrder(product.skuNumber, purchaseOrder);

    await test.step('Verify the child item is added with in bundle successfully', async () => {
      await purchaseOrder.verifyChildItems({ SKU: lineItem.sku, 'Line Text': lineItem.lineText }, product.bundleItems);
    });
  });

  test('Approve Purchase Order', async ({ purchaseOrder }) => {
    await openRandomDraftPurchaseOrderId(purchaseOrder, true);
    await test.step('Approve the Purchase Order', async () => {
      await purchaseOrder.clickApprove();
    });

    await test.step('Verify the Purchase Order is approved successfully', async () => {
      await purchaseOrder.verifyPurchaseOrderStatus(status().approved);
    });
  });

  test('Send Purchase Order to Approve flow', async ({ purchaseOrder }) => {
    const flowName = 'khaoco > admin flow';
    // precondition
    await test.step('Prepare the approve flow before testing', async () => {
      const exist: any = await getApproveFlow(flowName);
      const personIds = getBasedData()?.data?.person.filter(s => s.usingFor.includes('Approve flow'));
      let flowId: string = '';
      if (exist) {
        flowId = exist.ApprovalFlowId || '';
        // check if the flow has step or not
        const steps = await getStepByFlowId(flowId);
        if (steps.length > 0) {
          return;
        }
      } else {
        // Create approve flow
        flowId = await createApproveFlow(flowName, 'Flow for test automation');
      }
      // Added user to approve step
      if (personIds) {
        for (const personId of personIds) {
          await addApproveStep(flowId, personId.id);
        }
      }
    });

    await openRandomDraftPurchaseOrderId(purchaseOrder);

    await test.step(`Send the purchase order to approve flow: ${flowName}`, async () => {
      // approve
      await purchaseOrder.clickSendToApprove();
      await purchaseOrder.selectApproveFlowThenSend(flowName);
    });

    await test.step('Select the approve flow then send it to approver and verify the status changed to "Ready for approving"', async () => {
      await purchaseOrder.approveSelectedFlow();
      await purchaseOrder.verifyPurchaseOrderStatus(status().readyForApproving);
    });

    await test.step('Approve the PO and verify status changed to "Approved"', async () => {
      await purchaseOrder.clickApprove();
      await purchaseOrder.verifyPurchaseOrderStatus(status().approved);
    });
  });

  test('Send PO to Supplier who does not have email', async ({ purchaseOrder }) => {
    const companyId = data?.company.id || '';
    const contactTemp = {
      firstName: 'No Email',
      lastName: 'PW',
    };
    await openRandomDraftPurchaseOrderId(purchaseOrder, true);
    let contact: any;
    await test.step('Change supplier to a supplier who does not have email', async () => {
      contact = await getContract(companyId, false);
      if (!contact) {
        contact = await createContact(companyId, contactTemp.firstName, contactTemp.lastName);
      }
    });
    await test.step('Change supplier to a supplier who does not have email', async () => {
      mandatoryFields.internalNote = 'Update supplier to select the contact without email';
      await purchaseOrder.fillMandatoryFields(
        mandatoryFields.supplier,
        mandatoryFields.department,
        mandatoryFields.internalNote,
      );

      // change supplier contact
      await purchaseOrder.selectSupplierContact(`${contact?.Name || contact?.FullName || ''}`);
      await purchaseOrder.clickSave(status().draft);
    });

    await test.step('Send to Supplier and confirm the system enforces that the contact has an email address.', async () => {
      await purchaseOrder.clickSendToSupplier();
      await purchaseOrder.validateNoEmailInSupplier();
    });

    await test.step('Remove the Supplier Contact then Send to Supplier again also validate the system not allow', async () => {
      await purchaseOrder.selectSupplierContact('');
      await purchaseOrder.clickSave(status().draft);
      await purchaseOrder.clickSendToSupplier();
      await purchaseOrder.validateNoSupplier();
    });
  });

  test('Send PO to Supplier who have email', async ({ purchaseOrder }) => {
    const companyId = data?.company.id || '';
    const contactTemp = {
      firstName: 'Auto Email',
      lastName: 'PW',
      email: `<EMAIL>`,
    };
    await openRandomDraftPurchaseOrderId(purchaseOrder, true);
    let contact: any;
    await test.step('Change supplier to a supplier who have email', async () => {
      contact = await getContract(companyId, false);
      if (!contact) {
        contact = await createContact(companyId, contactTemp.firstName, contactTemp.lastName, contactTemp.email);
      }
    });
    await test.step('Change supplier to a supplier who have email', async () => {
      mandatoryFields.internalNote = 'Update supplier to select the contact with email';
      await purchaseOrder.fillMandatoryFields(
        mandatoryFields.supplier,
        mandatoryFields.department,
        mandatoryFields.internalNote,
      );

      // change supplier contact
      await purchaseOrder.selectSupplierContact(`${contact?.Name || contact?.FullName || ''}`);
      await purchaseOrder.clickSave(status().draft);
    });

    await test.step('Send to supplier', async () => {
      await purchaseOrder.clickSendToSupplier();
      await expectToastMessageContains(langPack().status.success);
    });

    await test.step('Trigger sending email to supplier', async () => {
      await triggerEmailJob();
      const toDay = getCurrentDateWithFormat('MM/dd/yyyy');
      const email = await getEmailFromHistory(toDay, toDay, {
        toEmail: contactTemp.email,
        templateName: 'PO send to supplier',
      });
    });
  });
});
