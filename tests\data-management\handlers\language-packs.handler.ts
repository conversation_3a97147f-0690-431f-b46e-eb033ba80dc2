// Get BASED_DATA_PATH from environment or default
import path from 'node:path';
import { loadJson } from '@common-helper';
import {
  LanguageRoot,
  LanguagePack,
  General,
  Button,
  CustomerWeb,
  Status,
  TabName,
  DialogTitle,
  PurchaseOrSalesOrder,
  Deal,
  Hour,
  Contact,
} from '@test-data/models/language-packs.model';

const LANGUAGE_PACKS_PATH =
  process.env['LANGUAGE_PACKS_PATH'] || path.resolve(process.cwd(), '.test-data/language_packs.json');
const LANGUAGE_CODE = process.env['LANG'] || 'en-us';

let cachedPacks: LanguageRoot | null = null;
let currentPack: LanguagePack | null = null;

/**
 * Determines language based on language code using contains logic
 * @param langCode - Language code (e.g., 'en-us', 'no-nb', 'en', 'no', 'nb')
 * @returns Mapped language name
 */
function mapLanguage(langCode?: string | null): string {
  const code = langCode?.toLowerCase() || LANGUAGE_CODE;

  if (code?.includes('en')) {
    return 'English';
  }

  if (code?.includes('no') || code?.includes('nb')) {
    return 'Norwegian';
  }

  // Default to English
  return 'English';
}

/**
 * Loads and caches all language packs
 */
export function getLanguagePacks(): LanguageRoot {
  if (!cachedPacks) {
    const arr = loadJson<LanguageRoot[]>(path.basename(LANGUAGE_PACKS_PATH), {
      baseDir: path.dirname(LANGUAGE_PACKS_PATH),
    });
    cachedPacks = {
      en: arr[0]!.en,
      no: arr[0]!.no,
    };
  }
  return cachedPacks;
}

/**
 * Gets the language pack for a given language code
 */
export function getLanguagePack(langCode?: string | null): LanguagePack {
  const mapped = mapLanguage(langCode);
  const packs = getLanguagePacks();

  if (mapped === 'Norwegian') {
    return packs.no;
  }
  return packs.en;
}

/**
 * Refreshes the current locale pack
 * @param langCode - Language code (e.g., 'en-us', 'no-nb')
 */
export function refreshLocale(langCode: string): void {
  currentPack = getLanguagePack(langCode);
}

/**
 * Exports the active language pack
 */
export function langPack(): LanguagePack {
  if (!currentPack) {
    return getLanguagePack();
  }
  return currentPack;
}

/**
 * Exports general translations (department, category, etc.)
 */
export function general(): General {
  return langPack().general;
}

/**
 * Exports button translations
 */
export function button(): Button {
  return langPack().buttons;
}

/**
 * Exports customer web translations
 */
export function customerWeb(): CustomerWeb {
  return langPack().customerWeb;
}

/**
 * Exports status translations
 */
export function status(): Status {
  return langPack().status;
}

/**
 * Exports tab name translations
 */
export function tabName(): TabName {
  return langPack().tabName;
}

/**
 * Exports dialog title translations
 */
export function dialogTitle(): DialogTitle {
  return langPack().dialogTitle;
}

/**
 * Exports purchase or sales order translations
 */
export function purchaseOrSalesOrder(): PurchaseOrSalesOrder {
  return langPack().purchaseOrSalesOrder;
}

/**
 * Exports deal translations
 */
export function deal(): Deal {
  return langPack().deal;
}

/**
 * Exports hour translations
 */
export function hour(): Hour {
  return langPack().hour;
}

/**
 * Exports contact translations
 */
export function contact(): Contact {
  return langPack().contact;
}
