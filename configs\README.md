# configs/

Purpose: Centralized configuration for Playwright projects, timeouts, environment setup/teardown.

- project-config.ts: defines Playwright projects (browsers and API)
- timeout.config.ts: single source of truth for timeouts
- global-setup.ts: loads environments/${NODE_ENV}.env
- global-teardown.ts: shared cleanup

Add new configs: create a new file and export what is needed; import via @configs/\* alias.

Guidelines:

- Never hardcode timeouts in tests; import from timeout.config.ts
- Use require. Resolve when referencing setup/teardown in playwright.config.ts for CI portability

Dependencies:

- Used by playwright.config.ts and utilities
