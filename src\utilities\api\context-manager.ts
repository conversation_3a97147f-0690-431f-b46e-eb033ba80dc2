import { request } from '@playwright/test';
import { handleException, logger, raiseError } from '@report-helper';
import type { AuthConfig, AuthContext } from '@custom-types';

// ===============================================
// Context Manager
// ===============================================

export class ContextManager {
  private contexts: Map<string, AuthContext> = new Map();
  private currentContextIndex = 0;

  /**
   * Create a new authentication context
   */
  async createContext(config: AuthConfig, baseUrl: string): Promise<string> {
    try {
      // Check if context already exists
      if (this.contexts.has(config.contextId)) {
        raiseError(`Context with ID '${config.contextId}' already exists`);
      }

      // Create Playwright API request context
      const context = await request.newContext({ baseURL: baseUrl });

      // Create auth context object
      const authContext: AuthContext = {
        id: config.contextId,
        config,
        context,
        baseUrl,
        isActive: true,
        createdAt: new Date(),
        usageCount: 0,
      };

      this.contexts.set(config.contextId, authContext);

      logger.info(`[Context Manager] Created context '${config.contextId}' with ${config.type} authentication`);
      return config.contextId;
    } catch (error) {
      handleException(error, `[Context Manager] Failed to create context '${config.contextId}'`);
    }
  }

  /**
   * Get a context by ID
   */
  getContext(contextId: string): AuthContext | undefined {
    return this.contexts.get(contextId);
  }

  /**
   * Get all contexts
   */
  getAllContexts(): AuthContext[] {
    return Array.from(this.contexts.values());
  }

  /**
   * Get active contexts
   */
  getActiveContexts(): AuthContext[] {
    return Array.from(this.contexts.values()).filter(ctx => ctx.isActive);
  }

  /**
   * Get the next context using simple round-robin rotation
   */
  getNextContext(): AuthContext | undefined {
    const activeContexts = this.getActiveContexts();

    if (activeContexts.length === 0) {
      return undefined;
    }

    // Simple round-robin rotation
    const selectedContext = activeContexts[this.currentContextIndex % activeContexts.length];
    if (!selectedContext) {
      return undefined;
    }

    this.currentContextIndex = (this.currentContextIndex + 1) % activeContexts.length;

    // Update usage tracking
    selectedContext.lastUsed = new Date();
    selectedContext.usageCount++;

    return selectedContext;
  }

  /**
   * Remove a context
   */
  async removeContext(contextId: string): Promise<void> {
    try {
      const authContext = this.contexts.get(contextId);
      if (!authContext) {
        logger.info(`[Context Manager] Context '${contextId}' not found for removal`);
        return;
      }

      // Dispose of the Playwright context
      await authContext.context.dispose();

      // Remove from our tracking
      this.contexts.delete(contextId);

      logger.info(`[Context Manager] Removed context '${contextId}'`);
    } catch (error) {
      handleException(error, `[Context Manager] Failed to remove context '${contextId}'`);
    }
  }

  /**
   * Deactivate a context (keep it but mark as inactive)
   */
  deactivateContext(contextId: string): void {
    const authContext = this.contexts.get(contextId);
    if (authContext) {
      authContext.isActive = false;
      logger.info(`[Context Manager] Deactivated context '${contextId}'`);
    }
  }

  /**
   * Activate a context
   */
  activateContext(contextId: string): void {
    const authContext = this.contexts.get(contextId);
    if (authContext) {
      authContext.isActive = true;
      logger.info(`[Context Manager] Activated context '${contextId}'`);
    }
  }

  /**
   * Dispose of all contexts
   */
  async dispose(): Promise<void> {
    try {
      const disposePromises = Array.from(this.contexts.values()).map(ctx =>
        ctx.context
          .dispose()
          .catch(error => logger.error(`[Context Manager] Error disposing context '${ctx.id}': ${error}`)),
      );

      await Promise.all(disposePromises);

      this.contexts.clear();
      this.currentContextIndex = 0;

      logger.info('[Context Manager] All contexts disposed');
    } catch (error) {
      handleException(error, '[Context Manager] Error during disposal');
    }
  }
}

// ===============================================
// Global Context Manager Instance
// ===============================================

let globalContextManager: ContextManager | null = null;

/**
 * Get or create the global context manager instance
 */
export function getGlobalContextManager(): ContextManager {
  if (!globalContextManager) {
    globalContextManager = new ContextManager();
  }
  return globalContextManager;
}

/**
 * Reset the global context manager instance
 */
export async function resetGlobalContextManager(): Promise<void> {
  if (globalContextManager) {
    await globalContextManager.dispose();
    globalContextManager = null;
  }
}
