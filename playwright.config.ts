import { defineConfig } from '@playwright/test';
import * as dotenv from 'dotenv';

import { PROJECT_CONFIG } from '@global-config';
import { ACTION_TIMEOUT, EXPECT_TIMEOUT, NAVIGATION_TIMEOUT, TEST_TIMEOUT } from '@global-timeout';
import { MONOCART_CONFIG } from '@report-helper';
import path from 'node:path';

const ENV_SELECTED = process.env['NODE_ENV'] || 'staging';
const IS_PIPELINE = !!process.env['IS_PIPELINE'];
const TAGS = process.env['TAGS'] ? new RegExp(process.env['TAGS']) : undefined;
const OUTPUT_DIR = `./test-results/${ENV_SELECTED}`;
const SCREENSHOT_PATH = process.env['SCREENSHOT_PATH'] || path.join(OUTPUT_DIR, 'screenshots');

switch (ENV_SELECTED) {
  case 'dev':
    dotenv.config({ path: './environments/dev.env' });
    break;
  case 'staging':
    dotenv.config({ path: './environments/staging.env' });
    break;
  case 'release':
    dotenv.config({ path: './environments/release.env' });
    break;
  default:
    dotenv.config({ path: './environments/dev.env' });
}

export const BASED_DATA_PATH =
  process.env['BASED_DATA_PATH'] || path.resolve(process.cwd(), '.test-data/based_data.json');
export const LANGUAGE_PACKS_PATH =
  process.env['LANGUAGE_PACKS_PATH'] || path.resolve(process.cwd(), '.test-data/language_packs.json');

export default defineConfig({
  testDir: './tests/test-management',
  outputDir: OUTPUT_DIR,
  fullyParallel: true,
  forbidOnly: IS_PIPELINE,
  workers: IS_PIPELINE ? 4 : 3,
  retries: IS_PIPELINE ? 1 : 0,
  ...(TAGS ? { grep: TAGS } : {}),
  metadata: {
    product: `Bravo Automated Test - ${process.env['NODE_ENV']}`,
    url: `${process.env['BASE_URL']}`,
    api: `${process.env['API_BASE_URL']}`,
    env: `${process.env['NODE_ENV']}` || 'dev',
    auth: `${process.env['AUTH_PATH']}` || `.auth/${process.env['NODE_ENV']}`,
    screenshotPath: SCREENSHOT_PATH,
  },
  // Add JUnit reporter for Azure Test Plans publishing while keeping Monocart
  reporter: [
    ['junit', { outputFile: `${OUTPUT_DIR}/junit/results.xml` }],
    ['monocart-reporter', MONOCART_CONFIG],
  ],
  use: {
    baseURL: process.env['BASE_URL'],
    navigationTimeout: NAVIGATION_TIMEOUT,
    actionTimeout: ACTION_TIMEOUT,
    // Rich artifacts on failure
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'off',
    bypassCSP: true,
    headless: IS_PIPELINE,
    acceptDownloads: true,
    ignoreHTTPSErrors: true,
    // Potential locale awareness (future) via process.env.LANG
  },
  expect: { timeout: EXPECT_TIMEOUT },
  timeout: TEST_TIMEOUT,
  projects: PROJECT_CONFIG,
  globalSetup: './configs/global-setup.ts',
});
