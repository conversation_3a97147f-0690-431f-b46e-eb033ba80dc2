# Environment Files

Holds `.env` style variable files loaded by `configs/global-setup.ts` based on `NODE_ENV`.

## Naming

- `dev.env`, `staging.env`, `release.env`, `db.env` etc.

## Usage

PowerShell:

```
$env:NODE_ENV = 'staging'
npx playwright test
```

`global-setup` loads `environments/${NODE_ENV}.env` first, then process-level vars override.

## Common Variables

```
BASE_URL=https://app.example.com
API_BASE_URL=https://api.example.com
MONGODB_CONNECTION_STRING=mongodb://localhost:27017
MONGODB_DATABASE_NAME=test_automation
```

Optional auth/password overrides used by account manager:

```
DEV_ADMIN_PASSWORD=...
STAGING_ADMIN_PASSWORD=...
RELEASE_ADMIN_PASSWORD=...
```

Keep secrets out of source control—supply via CI secret store where possible.
