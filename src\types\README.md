# Types Module

Central location for shared TypeScript types, global augmentations, and matcher interface extensions.

## Contents

- `custom-expect-types.d.ts` – Augments Playwright's `expect` with custom matchers (e.g., masking helpers). Loaded automatically via `tsconfig.json` `typeRoots` or `include`.
- `index.ts` – Re-export barrel for commonly shared domain/types if needed by consumers.
- `parameter-custom-api-types.ts` – Typed parameter/value structures for higher-level utilities (e.g., action option interfaces).

## Goals

1. Provide **strongly typed helpers** for UI/API/DB layers.
2. Centralize **custom matcher declarations** so editors/IDE pick them up without per-file imports.
3. Avoid leaking internal utility types into test files unless intentionally exported.

## Extending Custom Matchers

1. Implement matcher logic in `custom-expect.ts` (reporter folder).
2. Add the TypeScript signature in `custom-expect-types.d.ts`:

```ts
// custom-expect-types.d.ts
import 'expect-playwright'; // or playwright test types if needed

declare global {
  namespace PlaywrightTest {
    interface Matchers<R> {
      toHaveTrimmedText(expected: string): R;
    }
  }
}
```

3. Ensure matcher is registered via `expect.extend({ ... })`.
4. Restart TS server if editor doesn't pick up changes.

## Adding Shared Types

Place domain-neutral interfaces here (e.g., pagination, filtering, result envelopes). Example:

```ts
// index.ts
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}
```

## Import Patterns

Use path aliases defined in `tsconfig.json`:

```ts
import { PaginatedResult } from '@types';
```

Avoid deep relative imports like `../../types` from tests.

## Best Practices

- Keep files small and focused.
- Prefer interface over type aliases for object shapes (easier augmentation).
- Do not include runtime-only logic—types only.
- Document intent with concise JSDoc for exported shapes.

## Troubleshooting

| Issue                       | Cause                         | Fix                                         |
| --------------------------- | ----------------------------- | ------------------------------------------- |
| Matcher types not available | d.ts not included             | Check `tsconfig.json` `include`/`typeRoots` |
| Namespace collision         | Duplicate global augmentation | Consolidate into a single d.ts              |
| Intellisense stale          | Editor cache                  | Restart TS server / reload window           |

---

Keep this README updated when adding new global augmentations or shared core types.
