# Naming Conventions Guide

This guide outlines best practices for naming in TypeScript/JavaScript projects.

## Folder Names

- Use **kebab-case** (lowercase, hyphen-separated) for folder names.
  - Example: `page-objects`, `test-data`, `api-helpers`
- Avoid spaces and special characters.

## Class Names

- Use **PascalCase** (capitalize each word, no separators).
  - Example: `PurchaseOrderPage`, `ConfigHelper`, `UserService`
- Class names should be nouns or noun phrases.

## Object & Variable Names

- Use **camelCase** (first word lowercase, subsequent words capitalized).
  - Example: `userData`, `apiResponse`, `isLoggedIn`
- Use descriptive names that clearly indicate the variable’s purpose.
- Avoid single-letter names except for loop counters.

## Function Names

- Use **camelCase**.
  - Example: `getUserData()`, `fetchApiResponse()`
- Function names should be verbs or verb phrases.

## Constants

- Use **UPPER_CASE_SNAKE_CASE** for constants.
  - Example: `MAX_RETRIES`, `DEFAULT_TIMEOUT`

## File Names

- Use **kebab-case** for file names.
  - Example: `user-service.ts`, `api-helper.ts`

## General Tips

- Be consistent throughout the codebase.
- Avoid abbreviations unless they are well-known.
- To clarity over brevity.

---

Following these conventions will help keep your codebase clean, readable, and maintainable.
